{"timestamp": "2025-06-07T00:42:29.839Z", "testResults": {"sessionReady": true, "greetingReceived": true, "audioChunksSent": 10, "speechStartDetected": true, "speechStopDetected": false, "transcriptionReceived": false, "responseGenerated": true, "audioResponseReceived": true}, "messageFlow": {"totalMessages": 40, "sentMessages": ["start"], "receivedMessages": ["session.created", "ready", "session.updated", "conversation.item.created", "response.created", "response.output_item.added", "conversation.item.created", "response.content_part.added", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "rate_limits.updated", "response.audio.done", "response.audio_transcript.done", "response.content_part.done", "response.output_item.done", "response.done", "input_audio_buffer.speech_started"]}, "debugLog": ["[00:41:59] WebSocket connected", "[00:41:59] Sending start message", "[00:42:00] Message 1: session.created", "[00:42:00] OpenAI session created: ID: sess_BfbvEiWQisei3HhC3UERe", "[00:42:00] Message 2: ready", "[00:42:00] Session ready: Starting audio test in 2 seconds", "[00:42:00] Message 3: session.updated", "[00:42:00] Session configured: VAD: server_vad, threshold: 0.5", "[00:42:00] Message 4: conversation.item.created", "[00:42:00] Other message: conversation.item.created", "[00:42:00] Message 5: response.created", "[00:42:00] AI RESPONSE STARTED: OpenAI generating response", "[00:42:00] Message 6: response.output_item.added", "[00:42:00] Other message: response.output_item.added", "[00:42:00] Message 7: conversation.item.created", "[00:42:00] Other message: conversation.item.created", "[00:42:00] Message 8: response.content_part.added", "[00:42:00] Other message: response.content_part.added", "[00:42:00] Message 9: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 10: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 11: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 12: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 6400 chars", "[00:42:00] Message 13: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 14: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 15: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 9600 chars", "[00:42:00] Message 16: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 16000 chars", "[00:42:00] Message 17: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 16000 chars", "[00:42:00] Message 18: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 16000 chars", "[00:42:00] Message 19: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 20: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 21: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 22: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 23: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 16000 chars", "[00:42:00] Message 24: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 25: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 26: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 27: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 28: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:00] Message 29: response.audio.delta", "[00:42:00] AI AUDIO CHUNK: 32000 chars", "[00:42:00] Message 30: response.audio_transcript.delta", "[00:42:00] Other message: response.audio_transcript.delta", "[00:42:01] Message 31: response.audio.delta", "[00:42:01] AI AUDIO CHUNK: 48000 chars", "[00:42:01] Message 32: response.audio.delta", "[00:42:01] AI AUDIO CHUNK: 64000 chars", "[00:42:01] Message 33: response.audio.delta", "[00:42:01] AI AUDIO CHUNK: 59864 chars", "[00:42:01] Message 34: rate_limits.updated", "[00:42:01] Other message: rate_limits.updated", "[00:42:01] Message 35: response.audio.done", "[00:42:01] Other message: response.audio.done", "[00:42:01] Message 36: response.audio_transcript.done", "[00:42:01] AI GREETING RECEIVED: \"Hello! It's great to have you here. How are you feeling today?\"", "[00:42:01] Message 37: response.content_part.done", "[00:42:01] Other message: response.content_part.done", "[00:42:01] Message 38: response.output_item.done", "[00:42:01] Other message: response.output_item.done", "[00:42:01] Message 39: response.done", "[00:42:01] RESPONSE COMPLETE: AI finished responding", "[00:42:02] Starting audio test: Sending simulated speech audio", "[00:42:02] Audio chunk 1/10: 6400 chars", "[00:42:02] Message 40: input_audio_buffer.speech_started", "[00:42:02] SPEECH STARTED: OpenAI detected speech in audio buffer", "[00:42:02] Audio chunk 2/10: 6400 chars", "[00:42:02] Audio chunk 3/10: 6400 chars", "[00:42:02] Audio chunk 4/10: 6400 chars", "[00:42:02] Audio chunk 5/10: 6400 chars", "[00:42:02] Audio chunk 6/10: 6400 chars", "[00:42:02] Audio chunk 7/10: 6400 chars", "[00:42:02] Audio chunk 8/10: 6400 chars", "[00:42:03] Audio chunk 9/10: 6400 chars", "[00:42:03] Audio chunk 10/10: 6400 chars", "[00:42:03] Audio test complete: Sent 10 chunks", "[00:42:08] Ending test: Generating final report", "[00:42:08] WebSocket closed: 1005 ", "[00:42:29] TEST TIMEOUT: Ending test due to timeout", "[00:42:29] Ending test: Generating final report"]}