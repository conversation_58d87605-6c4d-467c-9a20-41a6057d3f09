{"timestamp": "2025-06-06T23:51:48.794Z", "testResults": {"sessionReady": true, "greetingReceived": true, "audioChunksSent": 10, "speechStartDetected": true, "speechStopDetected": true, "transcriptionReceived": true, "responseGenerated": true, "audioResponseReceived": true}, "messageFlow": {"totalMessages": 107, "sentMessages": ["start"], "receivedMessages": ["session.created", "ready", "session.updated", "conversation.item.created", "response.created", "response.output_item.added", "conversation.item.created", "response.content_part.added", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio.done", "response.audio_transcript.done", "response.content_part.done", "response.output_item.done", "response.done", "rate_limits.updated", "input_audio_buffer.speech_started", "input_audio_buffer.speech_stopped", "input_audio_buffer.committed", "conversation.item.created", "response.created", "response.output_item.added", "conversation.item.created", "response.content_part.added", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio_transcript.delta", "response.audio.delta", "response.audio.delta", "response.audio.delta", "response.audio.done", "response.audio_transcript.done", "response.content_part.done", "response.output_item.done", "response.done", "rate_limits.updated", "conversation.item.input_audio_transcription.delta", "conversation.item.input_audio_transcription.completed"]}, "debugLog": ["[23:51:18] WebSocket connected", "[23:51:18] Sending start message", "[23:51:19] Message 1: session.created", "[23:51:19] OpenAI session created: ID: sess_Bfb8BqqKJDg2boE6Luax9", "[23:51:19] Message 2: ready", "[23:51:19] Session ready: Starting audio test in 2 seconds", "[23:51:19] Message 3: session.updated", "[23:51:19] Session configured: VAD: server_vad, threshold: 0.5", "[23:51:19] Message 4: conversation.item.created", "[23:51:19] Other message: conversation.item.created", "[23:51:19] Message 5: response.created", "[23:51:19] AI RESPONSE STARTED: OpenAI generating response", "[23:51:19] Message 6: response.output_item.added", "[23:51:19] Other message: response.output_item.added", "[23:51:19] Message 7: conversation.item.created", "[23:51:19] Other message: conversation.item.created", "[23:51:19] Message 8: response.content_part.added", "[23:51:19] Other message: response.content_part.added", "[23:51:19] Message 9: response.audio_transcript.delta", "[23:51:19] Other message: response.audio_transcript.delta", "[23:51:19] Message 10: response.audio_transcript.delta", "[23:51:19] Other message: response.audio_transcript.delta", "[23:51:19] Message 11: response.audio_transcript.delta", "[23:51:19] Other message: response.audio_transcript.delta", "[23:51:19] Message 12: response.audio_transcript.delta", "[23:51:19] Other message: response.audio_transcript.delta", "[23:51:20] Message 13: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 6400 chars", "[23:51:20] Message 14: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 15: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 16: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 17: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 9600 chars", "[23:51:20] Message 18: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 16000 chars", "[23:51:20] Message 19: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 20: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 21: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 22: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 16000 chars", "[23:51:20] Message 23: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 16000 chars", "[23:51:20] Message 24: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 25: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 26: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 16000 chars", "[23:51:20] Message 27: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 28: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 29: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 30: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 31: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 32000 chars", "[23:51:20] Message 32: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 48000 chars", "[23:51:20] Message 33: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 34: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 35: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 36: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 37: response.audio_transcript.delta", "[23:51:20] Other message: response.audio_transcript.delta", "[23:51:20] Message 38: response.audio.delta", "[23:51:20] AI AUDIO CHUNK: 64000 chars", "[23:51:21] Message 39: response.audio.delta", "[23:51:21] AI AUDIO CHUNK: 64000 chars", "[23:51:21] Message 40: response.audio.delta", "[23:51:21] AI AUDIO CHUNK: 72664 chars", "[23:51:21] Message 41: response.audio.done", "[23:51:21] Other message: response.audio.done", "[23:51:21] Message 42: response.audio_transcript.done", "[23:51:21] AI GREETING RECEIVED: \"Hello! It's great to have you here. How are you feeling today? What's on your mind?\"", "[23:51:21] Message 43: response.content_part.done", "[23:51:21] Other message: response.content_part.done", "[23:51:21] Message 44: response.output_item.done", "[23:51:21] Other message: response.output_item.done", "[23:51:21] Message 45: response.done", "[23:51:21] RESPONSE COMPLETE: AI finished responding", "[23:51:21] Message 46: rate_limits.updated", "[23:51:21] Other message: rate_limits.updated", "[23:51:21] Starting audio test: Sending simulated speech audio", "[23:51:21] Audio chunk 1/10: 6400 chars", "[23:51:21] Message 47: input_audio_buffer.speech_started", "[23:51:21] SPEECH STARTED: OpenAI detected speech in audio buffer", "[23:51:21] Audio chunk 2/10: 6400 chars", "[23:51:21] Audio chunk 3/10: 6400 chars", "[23:51:21] Audio chunk 4/10: 6400 chars", "[23:51:21] Audio chunk 5/10: 6400 chars", "[23:51:21] Audio chunk 6/10: 6400 chars", "[23:51:21] Audio chunk 7/10: 6400 chars", "[23:51:21] Audio chunk 8/10: 6400 chars", "[23:51:22] Audio chunk 9/10: 6400 chars", "[23:51:22] Audio chunk 10/10: 6400 chars", "[23:51:22] Message 48: input_audio_buffer.speech_stopped", "[23:51:22] SPEECH STOPPED: OpenAI detected end of speech", "[23:51:22] Message 49: input_audio_buffer.committed", "[23:51:22] Other message: input_audio_buffer.committed", "[23:51:22] Message 50: conversation.item.created", "[23:51:22] Other message: conversation.item.created", "[23:51:22] Message 51: response.created", "[23:51:22] AI RESPONSE STARTED: OpenAI generating response", "[23:51:22] Audio test complete: Sent 10 chunks", "[23:51:22] Message 52: response.output_item.added", "[23:51:22] Other message: response.output_item.added", "[23:51:22] Message 53: conversation.item.created", "[23:51:22] Other message: conversation.item.created", "[23:51:22] Message 54: response.content_part.added", "[23:51:22] Other message: response.content_part.added", "[23:51:22] Message 55: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 56: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 57: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 58: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 59: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 6400 chars", "[23:51:22] Message 60: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 9600 chars", "[23:51:22] Message 61: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 16000 chars", "[23:51:22] Message 62: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 63: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 64: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 16000 chars", "[23:51:22] Message 65: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 16000 chars", "[23:51:22] Message 66: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 67: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 68: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 69: response.audio_transcript.delta", "[23:51:22] Other message: response.audio_transcript.delta", "[23:51:22] Message 70: response.audio.delta", "[23:51:22] AI AUDIO CHUNK: 16000 chars", "[23:51:23] Message 71: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 32000 chars", "[23:51:23] Message 72: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 73: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 74: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 75: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 76: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 77: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 78: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 79: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 80: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 48000 chars", "[23:51:23] Message 81: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 82: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 83: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 84: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 85: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 86: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 87: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 88: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 89: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 64000 chars", "[23:51:23] Message 90: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 64000 chars", "[23:51:23] Message 91: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 92: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 93: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 94: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 95: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 96: response.audio_transcript.delta", "[23:51:23] Other message: response.audio_transcript.delta", "[23:51:23] Message 97: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 64000 chars", "[23:51:23] Message 98: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 64000 chars", "[23:51:23] Message 99: response.audio.delta", "[23:51:23] AI AUDIO CHUNK: 47064 chars", "[23:51:23] Message 100: response.audio.done", "[23:51:23] Other message: response.audio.done", "[23:51:23] Message 101: response.audio_transcript.done", "[23:51:23] AI RESPONSE TRANSCRIBED: \"Take your time. I'm here when you're ready. Is there something specific you'd like to talk about, or would you prefer to start with how your day's been?\"", "[23:51:23] Message 102: response.content_part.done", "[23:51:23] Other message: response.content_part.done", "[23:51:23] Message 103: response.output_item.done", "[23:51:23] Other message: response.output_item.done", "[23:51:23] Message 104: response.done", "[23:51:23] RESPONSE COMPLETE: AI finished responding", "[23:51:23] Message 105: rate_limits.updated", "[23:51:23] Other message: rate_limits.updated", "[23:51:24] Message 106: conversation.item.input_audio_transcription.delta", "[23:51:24] Other message: conversation.item.input_audio_transcription.delta", "[23:51:24] Message 107: conversation.item.input_audio_transcription.completed", "[23:51:24] USER TRANSCRIBED: \"you\n\"", "[23:51:27] Ending test: Generating final report", "[23:51:27] WebSocket closed: 1005 ", "[23:51:48] TEST TIMEOUT: Ending test due to timeout", "[23:51:48] Ending test: Generating final report"]}