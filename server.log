nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
[STARTUP] Direct login route registered at /api/direct-login
12:11:49 AM [express] serving on port 5000
[AUTH] Login attempt at 2025-06-07T00:12:19.187Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: m9wzaD6vT6byufo3hcyWJZKdzidqGGEU
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbSYKsgCOxRDFQog3sg3
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hi there, it’s good to have you here. What’s on your mind today?
📝 Capturing transcript: ai said "Hi there, it’s good to have you here. What’s on your mind today?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Ending session...
OpenAI connection closed: 1005 
WebSocket connection closed
12:13:15 AM [express] GET /api/clients 401 in 1ms :: {"message":"Not authenticated"}
12:13:15 AM [express] GET /api/dashboard/stats 401 in 1ms :: {"message":"Not authenticated"}
12:13:15 AM [express] GET /api/notes 401 in 1ms :: {"message":"Not authenticated"}
[AUTH] Login attempt at 2025-06-07T00:13:17.961Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: Pqz0jZKxkRjwBBcceIRevWlRu3nApBGJ
12:13:18 AM [express] GET /api/dashboard/stats 200 in 86ms :: {"totalClients":0,"weeklyNotes":0,"insi…
12:13:18 AM [express] GET /api/clients 200 in 94ms :: []
12:13:18 AM [express] GET /api/notes 200 in 109ms :: []
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbTazgwUB0udErZQBAxi
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hey there, it's great to have you here. What’s been on your mind lately that you’d like to talk about?
📝 Capturing transcript: ai said "Hey there, it's great to have you here. What’s been on your mind lately that you’d like to talk about?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Ending session...
OpenAI connection closed: 1005 
12:14:56 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=S4NAJd_bYVbWtVEtWyo9Q
12:15:17 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=S4NAJd_bYVbWtVEtWyo9Q
