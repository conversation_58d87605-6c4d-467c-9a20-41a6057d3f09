nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
[STARTUP] Direct login route registered at /api/direct-login
12:19:34 AM [express] serving on port 5000
[AUTH] Login attempt at 2025-06-07T00:20:02.202Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: kgqsTuqyC9kqteKM0nH_tv4DL5Xdyzdp
12:20:02 AM [express] GET /api/dashboard/stats 304 in 84ms :: {"totalClients":0,"weeklyNotes":0,"insi…
12:20:02 AM [express] GET /api/clients 304 in 90ms :: []
12:20:02 AM [express] GET /api/notes 304 in 116ms :: []
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_Bfba6heNj8CWgnC4lw93e
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hi there! It’s great to have you here. What would you like to talk about today?
📝 Capturing transcript: ai said "Hi there! It’s great to have you here. What would you like to talk about today?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=10429.97, amp=10429.97, peaks=96.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=10429.97, amp=10429.97, peaks=96.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=10429.97, amp=10429.97, peaks=96.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=10429.97, amp=10429.97, peaks=96.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=10429.97, amp=10429.97, peaks=96.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
