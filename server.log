nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
[STARTUP] Direct login route registered at /api/direct-login
12:43:17 AM [express] serving on port 5000
[AUTH] Login attempt at 2025-06-07T00:43:50.101Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: 0V-Sl4gH5lvBIZt6t3P4e6SnfvtFpUNg
12:43:52 AM [express] GET /api/dashboard/stats 304 in 89ms :: {"totalClients":0,"weeklyNotes":0,"insi…
12:43:52 AM [express] GET /api/clients 304 in 92ms :: []
12:43:52 AM [express] GET /api/notes 304 in 109ms :: []
New WebSocket connection
Starting OpenAI Realtime API session...
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.31, amp=0.31, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Cannot send to OpenAI: not connected
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.80, amp=0.80, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Cannot send to OpenAI: not connected
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbxCVLNmWyEkPdUXmBjg
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.70, amp=0.70, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.42, amp=0.42, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=23.99, amp=23.99, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.21, amp=13.21, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.07, amp=9.07, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.78, amp=10.78, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.48, amp=12.48, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=14.74, amp=14.74, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 66264 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hi there, it’s good to have you here. What’s been on your mind lately?
📝 Capturing transcript: ai said "Hi there, it’s good to have you here. What’s been on your mind lately?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.47, amp=13.47, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.42, amp=13.42, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.00, amp=13.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=16.21, amp=16.21, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.91, amp=12.91, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.40, amp=13.40, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.93, amp=12.93, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=83.69, amp=83.69, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=45.04, amp=45.04, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.78, amp=12.78, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.81, amp=13.81, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.89, amp=13.89, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=13.24, amp=13.24, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=35.36, amp=35.36, peaks=0.12%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=612.45, amp=612.45, peaks=18.31%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=88.65, amp=88.65, peaks=0.34%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=175.79, amp=175.79, peaks=2.93%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=451.48, amp=451.48, peaks=16.77%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=175.99, amp=175.99, peaks=2.20%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=298.61, amp=298.61, peaks=3.03%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=20.50, amp=20.50, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.27, amp=4.27, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.20, amp=4.20, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.03, amp=4.03, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.06, amp=4.06, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.07, amp=4.07, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.06, amp=4.06, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.88, amp=3.88, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.24, amp=4.24, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.25, amp=4.25, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.05, amp=4.05, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: Start speaking.

📝 Capturing transcript: user said "Start speaking.
"
📝 Session now has 2 messages
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.35, amp=4.35, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.38, amp=4.38, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.23, amp=4.23, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.53, amp=4.53, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.05, amp=4.05, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 56664 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Of course, take your time. Whenever you're ready, I'm here to listen.
📝 Capturing transcript: ai said "Of course, take your time. Whenever you're ready, I'm here to listen."
📝 Session now has 3 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.44, amp=4.44, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.71, amp=4.71, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.64, amp=4.64, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.25, amp=4.25, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.58, amp=4.58, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.26, amp=4.26, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.15, amp=4.15, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.25, amp=4.25, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.22, amp=4.22, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.02, amp=4.02, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.40, amp=4.40, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.71, amp=4.71, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.27, amp=4.27, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.09, amp=4.09, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.94, amp=4.94, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.83, amp=3.83, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.64, amp=3.64, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.86, amp=3.86, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.54, amp=3.54, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.76, amp=3.76, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.48, amp=3.48, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.50, amp=3.50, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.84, amp=3.84, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.66, amp=3.66, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4.57, amp=4.57, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.68, amp=3.68, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.53, amp=3.53, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.95, amp=3.95, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.70, amp=3.70, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.58, amp=3.58, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.58, amp=3.58, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.82, amp=3.82, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.59, amp=3.59, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.76, amp=3.76, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.67, amp=3.67, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.43, amp=3.43, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3.64, amp=3.64, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.73, amp=5.73, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=83.16, amp=83.16, peaks=1.78%
🎯 Speech likelihood: LOW (energy=false, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1529.34, amp=1529.34, peaks=55.76%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=2146.63, amp=2146.63, peaks=69.38%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=2084.04, amp=2084.04, peaks=68.87%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1229.98, amp=1229.98, peaks=44.38%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=101.01, amp=101.01, peaks=2.61%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1573.45, amp=1573.45, peaks=49.27%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1464.58, amp=1464.58, peaks=43.46%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1522.93, amp=1522.93, peaks=57.18%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1386.31, amp=1386.31, peaks=52.17%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=758.71, amp=758.71, peaks=31.35%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=157.34, amp=157.34, peaks=3.61%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.37, amp=7.37, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.80, amp=7.80, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.01, amp=7.01, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.49, amp=7.49, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.19, amp=7.19, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.79, amp=7.79, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.20, amp=7.20, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: No, I can't hear anything

📝 Capturing transcript: user said "No, I can't hear anything
"
📝 Session now has 4 messages
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.17, amp=7.17, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.90, amp=6.90, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.34, amp=7.34, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.58, amp=7.58, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.07, amp=7.07, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.24, amp=9.24, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.66, amp=10.66, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.13, amp=8.13, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 85464 chars
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.93, amp=6.93, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Oh, I’m sorry about that. Let me make sure everything’s working on my end. Can you hear me now?
📝 Capturing transcript: ai said "Oh, I’m sorry about that. Let me make sure everything’s working on my end. Can you hear me now?"
📝 Session now has 5 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.34, amp=7.34, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.77, amp=7.77, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.67, amp=8.67, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=72.59, amp=72.59, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=98.41, amp=98.41, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=122.52, amp=122.52, peaks=0.00%
🎯 Speech likelihood: LOW (energy=true, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=62.36, amp=62.36, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=22.96, amp=22.96, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=138.89, amp=138.89, peaks=0.00%
🎯 Speech likelihood: LOW (energy=true, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=130.04, amp=130.04, peaks=0.00%
🎯 Speech likelihood: LOW (energy=true, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=20.79, amp=20.79, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=11.05, amp=11.05, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=3386.25, amp=3386.25, peaks=37.50%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=4986.79, amp=4986.79, peaks=70.09%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=368.21, amp=368.21, peaks=10.64%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=18.06, amp=18.06, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=25.72, amp=25.72, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.26, amp=8.26, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.76, amp=9.76, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.52, amp=8.52, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.48, amp=12.48, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.91, amp=10.91, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.58, amp=9.58, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.07, amp=8.07, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.02, amp=9.02, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.25, amp=7.25, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: She's here.

📝 Capturing transcript: user said "She's here.
"
📝 Session now has 6 messages
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.37, amp=9.37, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.26, amp=9.26, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.73, amp=7.73, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.31, amp=8.31, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.26, amp=8.26, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.26, amp=8.26, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.98, amp=8.98, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 88664 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Great, I’m glad you can hear me now. So, what’s been on your mind today?
📝 Capturing transcript: ai said "Great, I’m glad you can hear me now. So, what’s been on your mind today?"
📝 Session now has 7 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=19.55, amp=19.55, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.33, amp=9.33, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.71, amp=7.71, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.44, amp=10.44, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=88.29, amp=88.29, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=54.38, amp=54.38, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.46, amp=9.46, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.48, amp=8.48, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.18, amp=10.18, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.42, amp=10.42, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.44, amp=9.44, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.70, amp=8.70, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.98, amp=7.98, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.71, amp=6.71, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.22, amp=7.22, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.20, amp=7.20, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.06, amp=6.06, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.84, amp=5.84, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.05, amp=6.05, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.01, amp=7.01, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
