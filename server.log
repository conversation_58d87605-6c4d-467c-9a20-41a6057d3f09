nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
[STARTUP] Direct login route registered at /api/direct-login
12:46:08 AM [express] serving on port 5000
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbzTZuWWDrG6uehP1uIb
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 59864 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hello! It's great to have you here today. How are you feeling? What would you like to talk about?
📝 Capturing transcript: ai said "Hello! It's great to have you here today. How are you feeling? What would you like to talk about?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1520.99, amp=1520.99, peaks=52.75%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1532.23, amp=1532.23, peaks=53.04%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1519.33, amp=1519.33, peaks=52.33%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1529.83, amp=1529.83, peaks=52.79%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1528.76, amp=1528.76, peaks=52.63%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1530.53, amp=1530.53, peaks=52.21%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1525.41, amp=1525.41, peaks=52.50%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1527.31, amp=1527.31, peaks=51.54%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1527.61, amp=1527.61, peaks=52.50%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=1532.58, amp=1532.58, peaks=52.50%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: you

📝 Capturing transcript: user said "you
"
📝 Session now has 2 messages
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 85464 chars
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: I'm here to listen whenever you're ready. Take your time. What's been on your mind lately?
📝 Capturing transcript: ai said "I'm here to listen whenever you're ready. Take your time. What's been on your mind lately?"
📝 Session now has 3 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
WebSocket connection closed
OpenAI connection closed: 1005 
[AUTH] Login attempt at 2025-06-07T00:47:13.108Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: fN8_HH7g5qCDftdsbmlwSeO5cEFqaQnv
New WebSocket connection
Starting OpenAI Realtime API session...
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Cannot send to OpenAI: not connected
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_Bfc0QA9HfPhAJDdGW8SZk
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=0.00, amp=0.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=2965.77, amp=2965.77, peaks=13.43%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1.73, amp=1.73, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=10.91, amp=10.91, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=823.54, amp=823.54, peaks=28.91%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=568.54, amp=568.54, peaks=18.92%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.98, amp=5.98, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=48.35, amp=48.35, peaks=0.51%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1006.09, amp=1006.09, peaks=42.07%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: input_audio_buffer.speech_started
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hey there, it’s good to have you here today. What’s been on your mind lately?
📝 Capturing transcript: ai said "Hey there, it’s good to have you here today. What’s been on your mind lately?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1902.84, amp=1902.84, peaks=73.51%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=576.89, amp=576.89, peaks=23.97%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.10, amp=6.10, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.61, amp=6.61, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.87, amp=8.87, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.43, amp=6.43, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.89, amp=5.89, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.97, amp=5.97, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=19.97, amp=19.97, peaks=0.24%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=12.27, amp=12.27, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.72, amp=5.72, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.69, amp=5.69, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: Hi, can you hear me?

📝 Capturing transcript: user said "Hi, can you hear me?
"
📝 Session now has 2 messages
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.83, amp=5.83, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.82, amp=5.82, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.73, amp=9.73, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 43864 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Yes, I can hear you clearly. How are you feeling today?
📝 Capturing transcript: ai said "Yes, I can hear you clearly. How are you feeling today?"
📝 Session now has 3 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.01, amp=6.01, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.00, amp=6.00, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.68, amp=5.68, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=5.70, amp=5.70, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.34, amp=6.34, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.40, amp=6.40, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1411.73, amp=1411.73, peaks=47.44%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=39.77, amp=39.77, peaks=0.46%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=22.75, amp=22.75, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.35, amp=8.35, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.21, amp=7.21, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.89, amp=7.89, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.93, amp=6.93, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.38, amp=7.38, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.62, amp=6.62, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: No.

📝 Capturing transcript: user said "No.
"
📝 Session now has 4 messages
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.53, amp=7.53, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.79, amp=6.79, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=41.65, amp=41.65, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=656.92, amp=656.92, peaks=22.95%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: input_audio_buffer.speech_started
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: That’s okay. Sometimes it can be tough to pin down how we’re feeling.
📝 Capturing transcript: ai said "That’s okay. Sometimes it can be tough to pin down how we’re feeling."
📝 Session now has 5 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=915.41, amp=915.41, peaks=32.71%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1302.68, amp=1302.68, peaks=48.75%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=744.71, amp=744.71, peaks=25.78%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=387.44, amp=387.44, peaks=8.86%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1162.62, amp=1162.62, peaks=43.48%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1125.29, amp=1125.29, peaks=41.89%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=1082.70, amp=1082.70, peaks=40.65%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=590.95, amp=590.95, peaks=13.40%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=528.54, amp=528.54, peaks=18.90%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=228.83, amp=228.83, peaks=4.74%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=63.91, amp=63.91, peaks=0.17%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=14.24, amp=14.24, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: input_audio_buffer.speech_stopped
OpenAI → Client: input_audio_buffer.committed
Unhandled OpenAI message type: input_audio_buffer.committed
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.34, amp=7.34, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.36, amp=7.36, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.07, amp=7.07, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.27, amp=7.27, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: conversation.item.input_audio_transcription.delta
Unhandled OpenAI message type: conversation.item.input_audio_transcription.delta
OpenAI → Client: conversation.item.input_audio_transcription.completed
Capturing user transcript: So let's try this again.

📝 Capturing transcript: user said "So let's try this again.
"
📝 Session now has 6 messages
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.11, amp=7.11, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.44, amp=7.44, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.78, amp=7.78, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.17, amp=7.17, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.output_item.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
🔊 AUDIO DELTA RECEIVED: NO DELTA
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.27, amp=7.27, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 6400 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 9600 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.38, amp=7.38, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.43, amp=7.43, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 16000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 32000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.77, amp=7.77, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 48000 chars
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.42, amp=7.42, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 64000 chars
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.82, amp=7.82, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
OpenAI → Client: response.audio.delta
🔊 AUDIO DELTA RECEIVED: 53464 chars
OpenAI → Client: response.audio.done
🔊 AUDIO DONE RECEIVED
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Absolutely, take your time. What’s been on your mind lately?
📝 Capturing transcript: ai said "Absolutely, take your time. What’s been on your mind lately?"
📝 Session now has 7 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.41, amp=7.41, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.79, amp=7.79, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.42, amp=7.42, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.60, amp=7.60, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.34, amp=7.34, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.47, amp=7.47, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.69, amp=7.69, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.38, amp=7.38, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=9.89, amp=9.89, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=8.13, amp=8.13, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.95, amp=6.95, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.61, amp=6.61, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.68, amp=6.68, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.57, amp=6.57, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.71, amp=6.71, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=7.20, amp=7.20, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.73, amp=6.73, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.87, amp=6.87, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.57, amp=6.57, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (10924 chars)
📊 Audio info: 8192 bytes, 4096 samples
🔊 Audio analysis: avg=6.12, amp=6.12, peaks=0.00%
🎯 Speech likelihood: LOW (energy=false, peaks=false)
Ending session...
OpenAI connection closed: 1005 
Ending session...
