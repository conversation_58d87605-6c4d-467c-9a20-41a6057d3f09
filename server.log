nohup: ignoring input
OpenAI SDK Version: OpenAI
Using model: gpt-4o-mini for regular calls
API Key configured: Yes
PostgreSQL direct connection established
Admin user already exists
[STARTUP] Admin user check complete
[STARTUP] Direct login route registered at /api/direct-login
11:57:09 PM [express] serving on port 5000
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbDzM9oiDwDPFWrx6uq8
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2030.95, amp=2030.95, peaks=62.83%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
OpenAI → Client: response.audio.delta
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hello! It's great to have you here. How are you feeling today? Is there anything in particular you'd like to talk about?
📝 Capturing transcript: ai said "Hello! It's great to have you here. How are you feeling today? Is there anything in particular you'd like to talk about?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
OpenAI → Client: input_audio_buffer.speech_started
OpenAI → Client: response.done
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2036.61, amp=2036.61, peaks=62.25%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2040.25, amp=2040.25, peaks=63.00%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2041.01, amp=2041.01, peaks=62.25%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2037.57, amp=2037.57, peaks=62.54%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2036.84, amp=2036.84, peaks=62.83%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2035.77, amp=2035.77, peaks=62.17%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
Realtime proxy handling message: input_audio_buffer.append
📤 Forwarding audio to OpenAI: string (6400 chars)
📊 Audio info: 4800 bytes, 2400 samples
🔊 Audio analysis: avg=2041.45, amp=2041.45, peaks=62.67%
🎯 Speech likelihood: HIGH (energy=true, peaks=true)
WebSocket connection closed
OpenAI connection closed: 1005 
[AUTH] Login attempt at 2025-06-07T00:00:34.143Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: O1xHdLPuZgK1ckK0QfOFV8B0d6UpTcq6
12:00:37 AM [express] GET /api/clients 304 in 97ms :: []
12:00:37 AM [express] GET /api/notes 304 in 133ms :: []
12:00:39 AM [express] GET /api/dashboard/stats 304 in 100ms :: {"totalClients":0,"weeklyNotes":0,"ins…
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbHHB2huK63ctD3KqVTZ
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hi there, it's good to have you here. What’s been on your mind lately?
📝 Capturing transcript: ai said "Hi there, it's good to have you here. What’s been on your mind lately?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
Ending session...
OpenAI connection closed: 1005 
WebSocket connection closed
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbIfclQacFlCxS6akSPf
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: rate_limits.updated
OpenAI rate limits updated
OpenAI → Client: response.audio.done
OpenAI → Client: response.audio_transcript.done
Capturing AI transcript: Hi there, it’s good to have you here. What’s been on your mind lately?
📝 Capturing transcript: ai said "Hi there, it’s good to have you here. What’s been on your mind lately?"
📝 Session now has 1 messages
OpenAI → Client: response.content_part.done
OpenAI → Client: response.output_item.done
OpenAI → Client: response.done
12:03:58 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=bLTr6YsaE6FXVUErvfoGT
12:04:15 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=bLTr6YsaE6FXVUErvfoGT
12:05:14 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=bLTr6YsaE6FXVUErvfoGT
12:05:35 AM [vite] hmr update /src/pages/AdminAITest.tsx, /src/index.css?v=bLTr6YsaE6FXVUErvfoGT
WebSocket connection closed
OpenAI connection closed: 1005 
12:06:17 AM [express] GET /api/notes 401 in 1ms :: {"message":"Not authenticated"}
12:06:17 AM [express] GET /api/dashboard/stats 401 in 1ms :: {"message":"Not authenticated"}
12:06:17 AM [express] GET /api/clients 401 in 1ms :: {"message":"Not authenticated"}
[AUTH] Login attempt at 2025-06-07T00:06:19.976Z
[AUTH] Login request body: { username: 'admin', password: 'admin123' }
[AUTH] Login attempt: admin
[AUTH] User found: admin (ID: 1)
[AUTH] Login successful: admin (ID: 1)
[AUTH] User authenticated successfully: { id: 1, username: 'admin', role: 'admin' }
[AUTH] Login successful, session created
[AUTH] Session ID: kgn8WlCM9XZBvHpjvAx4dfxl-g2oUp9L
12:06:20 AM [express] GET /api/clients 200 in 93ms :: []
12:06:20 AM [express] GET /api/dashboard/stats 200 in 93ms :: {"totalClients":0,"weeklyNotes":0,"insi…
12:06:20 AM [express] GET /api/notes 200 in 102ms :: []
New WebSocket connection
Starting OpenAI Realtime API session...
Connected to OpenAI Realtime API
🎯 Configuring session with SERVER-SIDE VAD (OpenAI handles speech detection)...
📊 Turn Detection: ENABLED (threshold: 0.5, silence: 500ms)
✅ Server-side VAD configured - OpenAI will handle speech detection
OpenAI Realtime API session initialized
OpenAI → Client: session.created
OpenAI session created: sess_BfbMlF0zhpTsGCMPw4hib
OpenAI → Client: session.updated
OpenAI session updated
Triggering initial AI greeting...
OpenAI → Client: conversation.item.created
OpenAI → Client: response.created
OpenAI → Client: response.output_item.added
OpenAI → Client: conversation.item.created
OpenAI → Client: response.content_part.added
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
OpenAI → Client: response.audio_transcript.delta
