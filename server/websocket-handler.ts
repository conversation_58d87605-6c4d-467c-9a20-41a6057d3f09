import { WebSocket } from 'ws';
import { OpenAIRealtimeProxy } from './openai-realtime-proxy';

// Simple interface for WebSocket messages
interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

// Store for Realtime API proxies and session data
const realtimeProxies = new Map<WebSocket, OpenAIRealtimeProxy>();
const sessionTranscripts = new Map<WebSocket, {
  userId: string;
  clientId: string;
  messages: Array<{
    speaker: 'user' | 'ai';
    text: string;
    timestamp: Date;
  }>;
  startTime: Date;
}>();

export const handleWebSocketConnection = (ws: WebSocket) => {
  console.log('New WebSocket connection');
  
  ws.onmessage = async (event) => {
    try {
      const message = JSON.parse(event.data.toString()) as WebSocketMessage;
      const messageType = message.type as string;
      
      // Get existing proxy if available
      const realtimeProxy = realtimeProxies.get(ws);
      
      switch (messageType) {
        case 'start':
          await handleSessionStart(ws, message);
          break;
          
        case 'conversationEnded':
          await handleSessionEnd(ws);
          break;
          
        case 'summary_request':
          await handleSummaryRequest(ws);
          break;
          
        case 'test_audio':
          await handleTestAudio(ws, message);
          break;
          
        case 'ping':
          ws.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().toISOString()
          }));
          break;
          
        default:
          // Route all other messages to Realtime proxy if exists
          if (realtimeProxy) {
            realtimeProxy.handleClientMessage(message);
          } else {
            console.log(`Received message but no active session: ${messageType}`);
          }
          break;
      }
    } catch (error) {
      console.error('Error handling WebSocket message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };
  
  ws.onclose = () => {
    console.log('WebSocket connection closed');
    cleanupSession(ws);
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket error:', error);
    cleanupSession(ws);
  };
};

async function handleSessionStart(ws: WebSocket, message: any) {
  console.log('Starting OpenAI Realtime API session...');
  
  try {
    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    // Extract user info
    const userId = message.userId?.toString() || '1';
    const clientId = message.clientId?.toString() || '1';

    // Initialize session transcript tracking
    sessionTranscripts.set(ws, {
      userId,
      clientId,
      messages: [],
      startTime: new Date()
    });

    // Create Realtime API configuration
    const realtimeConfig = {
      apiKey: openaiApiKey,
      model: message.behavior?.model || 'gpt-4o-realtime-preview',
      voice: message.behavior?.voice?.voice || 'shimmer',
      instructions: message.instructions || 'You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts.',
      temperature: message.behavior?.temperature || 0.7
    };

    // Create and connect proxy with transcript tracking
    const realtimeProxy = new OpenAIRealtimeProxy(realtimeConfig);
    realtimeProxies.set(ws, realtimeProxy);
    
    // Hook into proxy to capture transcript
    realtimeProxy.onTranscriptUpdate = (speaker: 'user' | 'ai', text: string) => {
      console.log(`📝 Capturing transcript: ${speaker} said "${text}"`);
      const session = sessionTranscripts.get(ws);
      if (session) {
        session.messages.push({
          speaker,
          text,
          timestamp: new Date()
        });
        console.log(`📝 Session now has ${session.messages.length} messages`);
      }
    };
    
    await realtimeProxy.connect(ws);
    console.log('OpenAI Realtime API session initialized');
    
  } catch (error) {
    console.error('Failed to initialize OpenAI Realtime API:', error);

    // Enhanced error reporting for debugging
    let errorMessage = 'Failed to initialize real-time session: ';
    if (error instanceof Error) {
      errorMessage += error.message;
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    } else {
      errorMessage += 'Unknown error';
    }

    // Check if this is an API access issue
    if (error instanceof Error && (
      error.message.includes('401') ||
      error.message.includes('403') ||
      error.message.includes('Unauthorized') ||
      error.message.includes('realtime')
    )) {
      errorMessage += '\n\nNOTE: The OpenAI Realtime API requires special beta access. Your API key may not have Realtime API permissions.';
    }

    ws.send(JSON.stringify({
      type: 'error',
      message: errorMessage
    }));
  }
}

async function handleSessionEnd(ws: WebSocket) {
  console.log('Ending session...');
  
  const realtimeProxy = realtimeProxies.get(ws);
  if (realtimeProxy) {
    realtimeProxy.disconnect();
    realtimeProxies.delete(ws);
  }
  
  // Send acknowledgment
  ws.send(JSON.stringify({
    type: 'end_ack'
  }));
}

async function handleSummaryRequest(ws: WebSocket) {
  console.log('Generating session summary...');
  
  const session = sessionTranscripts.get(ws);
  console.log(`📋 Session transcript has ${session?.messages.length || 0} messages`);
  
  if (!session || session.messages.length === 0) {
    console.log('📋 No transcript to summarize - sending empty response');
    ws.send(JSON.stringify({
      type: 'summary',
      content: 'No conversation to summarize.'
    }));
    return;
  }

  try {
    // Use traditional OpenAI API to generate summary
    const { summarizeTherapySession } = await import('./summary-service');
    const summary = await summarizeTherapySession(session.messages, {
      userId: session.userId,
      clientId: session.clientId,
      sessionDuration: Date.now() - session.startTime.getTime()
    });
    
    ws.send(JSON.stringify({
      type: 'summary',
      content: summary
    }));
    
    // Clean up session data after summary
    sessionTranscripts.delete(ws);
    
  } catch (error) {
    console.error('Error generating summary:', error);
    ws.send(JSON.stringify({
      type: 'summary',
      content: 'Failed to generate summary: ' + (error instanceof Error ? error.message : 'Unknown error')
    }));
  }
}

async function handleTestAudio(ws: WebSocket, message: any) {
  try {
    const { generateSpeechFile } = await import('./improved-voice-api');
    
    const testText = message.text || "This is a test of the audio output. Can you hear this clearly?";
    const voice = message.voice || "shimmer";
    const speed = message.speed || 1.0;
    
    const audioUrl = await generateSpeechFile(testText, voice, speed);
    
    ws.send(JSON.stringify({
      type: 'test_audio_response',
      audioUrl,
      text: testText,
      voice
    }));
    
    console.log('Sent test audio response');
  } catch (error) {
    console.error('Error generating test audio:', error);
    ws.send(JSON.stringify({
      type: 'error',
      message: 'Failed to generate test audio',
      details: error instanceof Error ? error.message : 'Unknown error'
    }));
  }
}

function cleanupSession(ws: WebSocket) {
  const realtimeProxy = realtimeProxies.get(ws);
  if (realtimeProxy) {
    realtimeProxy.disconnect();
    realtimeProxies.delete(ws);
  }
  
  sessionTranscripts.delete(ws);
} 