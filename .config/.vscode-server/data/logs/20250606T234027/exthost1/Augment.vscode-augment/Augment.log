2025-06-06 23:40:31.780 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-06 23:40:31.780 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-06-06 23:40:31.780 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","enableAgentAutoMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5}
2025-06-06 23:40:31.842 [info] 'AugmentExtension' Retrieving model config
2025-06-06 23:40:31.991 [info] 'AugmentConfigListener' settings parsed successfully
2025-06-06 23:40:32.448 [info] 'AugmentExtension' Retrieved model config
2025-06-06 23:40:32.448 [info] 'AugmentExtension' Returning model config
2025-06-06 23:40:32.539 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
2025-06-06 23:40:32.539 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-06 23:40:32.539 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-06-06 23:40:32.539 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-06-06 23:40:32.539 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 6/5/2025, 1:28:15 AM; type = explicit
2025-06-06 23:40:32.539 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-06-06 23:40:32.539 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 6/5/2025, 1:28:15 AM
2025-06-06 23:40:32.557 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-06-06 23:40:32.557 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-06-06 23:40:32.557 [info] 'HotKeyHints' HotKeyHints initialized
2025-06-06 23:40:32.557 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-06-06 23:40:32.581 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-06 23:40:32.581 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-06 23:40:32.999 [info] 'WorkspaceManager[workspace]' Start tracking
2025-06-06 23:40:33.068 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-06-06 23:40:33.068 [info] 'OpenFileManager' Opened source folder 100
2025-06-06 23:40:33.070 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-06 23:40:33.087 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-06-06 23:40:33.087 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-06-06 23:40:33.087 [info] 'TaskManager' Setting current root task UUID to 041097f4-3bc9-42b8-a715-f0e0f98e1b58
2025-06-06 23:40:33.103 [info] 'MtimeCache[workspace]' read 2089 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-06-06 23:40:33.476 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-06-06 23:40:33.476 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-06-06 23:40:33.476 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-06 23:40:33.476 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-06 23:40:33.494 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-06-06 23:40:33.494 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-06-06 23:40:39.336 [info] 'StallDetector' Recent work: [{"name":"find-symbol-request","durationMs":3969.374443,"timestamp":"2025-06-06T23:40:39.260Z"},{"name":"find-symbol-request","durationMs":3969.120383,"timestamp":"2025-06-06T23:40:39.260Z"},{"name":"find-symbol-request","durationMs":3968.905043,"timestamp":"2025-06-06T23:40:39.260Z"},{"name":"find-symbol-request","durationMs":3968.663123,"timestamp":"2025-06-06T23:40:39.260Z"},{"name":"find-symbol-request","durationMs":3968.483423,"timestamp":"2025-06-06T23:40:39.260Z"}]
2025-06-06 23:40:51.361 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-06-06 23:40:51.361 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 632
  - files emitted: 2076
  - other paths emitted: 4
  - total paths emitted: 2712
  - timing stats:
    - readDir: 13 ms
    - filter: 79 ms
    - yield: 17 ms
    - total: 118 ms
2025-06-06 23:40:51.361 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1635
  - paths not accessible: 0
  - not plain files: 0
  - large files: 34
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1593
  - mtime cache misses: 42
  - probe batches: 6
  - blob names probed: 1658
  - files read: 494
  - blobs uploaded: 23
  - timing stats:
    - ingestPath: 2 ms
    - probe: 1916 ms
    - stat: 29 ms
    - read: 3816 ms
    - upload: 1776 ms
2025-06-06 23:40:51.361 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 72 ms
  - read MtimeCache: 33 ms
  - pre-populate PathMap: 126 ms
  - create PathFilter: 415 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 125 ms
  - purge stale PathMap entries: 7 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 17581 ms
  - enable persist: 3 ms
  - total: 18362 ms
2025-06-06 23:40:51.361 [info] 'WorkspaceManager' Workspace startup complete in 18837 ms
2025-06-06 23:43:45.004 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-06 23:43:45.088 [info] 'TaskManager' Setting current root task UUID to 7654dc6b-0544-4805-8c84-dccdb1050436
2025-06-06 23:43:45.089 [info] 'TaskManager' Setting current root task UUID to 7654dc6b-0544-4805-8c84-dccdb1050436
2025-06-06 23:46:50.730 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-06 23:47:04.063 [info] 'ViewTool' Tool called with path: server and view_range: undefined
2025-06-06 23:47:04.121 [info] 'ViewTool' Listing directory: server (depth: 2, showHidden: false)
2025-06-06 23:47:08.191 [info] 'ViewTool' Tool called with path: server/index.ts and view_range: undefined
2025-06-06 23:47:28.089 [info] 'ToolFileUtils' Reading file: debug-realtime-conversation.js
2025-06-06 23:47:28.299 [info] 'ToolFileUtils' Successfully read file: debug-realtime-conversation.js (4876 bytes)
2025-06-06 23:48:05.248 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/checkpoint-documents/58d87605-6c4d-467c-9a20-41a6057d3f09
2025-06-06 23:48:47.579 [info] 'ViewTool' Tool called with path: package.json and view_range: undefined
2025-06-06 23:49:04.480 [info] 'ViewTool' Tool called with path: .env and view_range: undefined
2025-06-06 23:49:04.538 [info] 'ViewTool' Path does not exist: .env
2025-06-06 23:49:09.113 [info] 'ViewTool' Tool called with path: .env.example and view_range: undefined
2025-06-06 23:49:09.174 [info] 'ViewTool' Path does not exist: .env.example
2025-06-06 23:51:57.685 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: undefined
2025-06-06 23:52:21.227 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:52:21.228 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (68949 bytes)
2025-06-06 23:52:23.173 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:52:23.174 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (70004 bytes)
2025-06-06 23:52:46.092 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:52:46.092 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (70004 bytes)
2025-06-06 23:52:51.400 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [945,1000]
2025-06-06 23:53:29.264 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250606T234027/exthost1/output_logging_20250606T234029
2025-06-06 23:53:55.219 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:53:55.220 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (70004 bytes)
2025-06-06 23:53:56.835 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:53:56.836 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (71056 bytes)
2025-06-06 23:54:20.641 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:54:20.641 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (71056 bytes)
2025-06-06 23:54:22.235 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:54:22.235 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (70912 bytes)
2025-06-06 23:54:49.086 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:54:49.087 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (70912 bytes)
2025-06-06 23:54:50.694 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:54:50.694 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (74042 bytes)
2025-06-06 23:55:01.515 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:55:01.516 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (74042 bytes)
2025-06-06 23:55:03.215 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-06 23:55:03.216 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (74428 bytes)
2025-06-06 23:55:37.169 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-06 23:55:37.338 [info] 'TaskManager' Setting current root task UUID to 43356682-8d01-4648-a7e8-a81eda8bf8fa
2025-06-06 23:55:37.338 [info] 'TaskManager' Setting current root task UUID to 43356682-8d01-4648-a7e8-a81eda8bf8fa
2025-06-06 23:56:45.717 [info] 'TaskManager' Setting current root task UUID to 7654dc6b-0544-4805-8c84-dccdb1050436
2025-06-06 23:56:45.717 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-06-07 00:03:57.684 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:03:57.943 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (74428 bytes)
2025-06-07 00:03:59.758 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:03:59.758 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (75547 bytes)
2025-06-07 00:04:03.055 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e8274c91-d68b-4f36-9c78-7bab703ffbc6.json'
2025-06-07 00:04:15.055 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:04:15.055 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (75547 bytes)
2025-06-07 00:04:16.669 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:04:16.669 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (76233 bytes)
2025-06-07 00:04:20.084 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e8274c91-d68b-4f36-9c78-7bab703ffbc6.json'
2025-06-07 00:05:00.761 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1048,1090]
2025-06-07 00:05:14.433 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:05:14.433 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (76233 bytes)
2025-06-07 00:05:16.112 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:05:16.113 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (76818 bytes)
2025-06-07 00:05:19.514 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e8274c91-d68b-4f36-9c78-7bab703ffbc6.json'
2025-06-07 00:05:24.647 [info] 'ViewTool' Tool called with path: client/src/pages/AdminAITest.tsx and view_range: [1086,1095]
2025-06-07 00:05:35.285 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:05:35.286 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (76818 bytes)
2025-06-07 00:05:36.981 [info] 'ToolFileUtils' Reading file: client/src/pages/AdminAITest.tsx
2025-06-07 00:05:36.982 [info] 'ToolFileUtils' Successfully read file: client/src/pages/AdminAITest.tsx (77110 bytes)
2025-06-07 00:05:40.403 [error] 'ShardManager' Failed to flush shards: ENOENT: no such file or directory, unlink '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e8274c91-d68b-4f36-9c78-7bab703ffbc6.json'
