2025-06-06 23:40:32.365 [info] [main] Log level: Info
2025-06-06 23:40:32.365 [info] [main] Validating found git in: "git"
2025-06-06 23:40:32.365 [info] [main] Using git "2.47.2" from "git"
2025-06-06 23:40:32.365 [info] [Model][doInitialScan] Initial repository scan started
2025-06-06 23:40:32.365 [info] > git rev-parse --show-toplevel [9ms]
2025-06-06 23:40:32.365 [info] > git rev-parse --git-dir --git-common-dir [313ms]
2025-06-06 23:40:32.365 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-06 23:40:32.365 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-06 23:40:32.365 [info] > git rev-parse --show-toplevel [62ms]
2025-06-06 23:40:32.365 [info] > git config --get commit.template [83ms]
2025-06-06 23:40:32.366 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-06 23:40:32.626 [info] > git status -z -uall [35ms]
2025-06-06 23:40:32.626 [info] > git rev-parse --show-toplevel [184ms]
2025-06-06 23:40:32.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [43ms]
2025-06-06 23:40:32.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-06 23:40:32.683 [info] > git rev-parse --show-toplevel [44ms]
2025-06-06 23:40:32.829 [info] > git config --get commit.template [158ms]
2025-06-06 23:40:32.895 [info] > git check-ignore -v -z --stdin [61ms]
2025-06-06 23:40:32.896 [info] > git rev-parse --show-toplevel [199ms]
2025-06-06 23:40:32.896 [info] > git config --get --local branch.main.vscode-merge-base [213ms]
2025-06-06 23:40:32.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-06-06 23:40:32.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [34ms]
2025-06-06 23:40:32.951 [info] > git rev-parse --show-toplevel [19ms]
2025-06-06 23:40:32.951 [info] > git merge-base refs/heads/main refs/remotes/origin/main [11ms]
2025-06-06 23:40:32.977 [info] > git diff --name-status -z --diff-filter=ADMR 914f82aa2133a3ab9e76799ac1b0b9bf2d295a95...refs/remotes/origin/main [20ms]
2025-06-06 23:40:33.002 [info] > git rev-parse --show-toplevel [37ms]
2025-06-06 23:40:33.056 [info] > git status -z -uall [70ms]
2025-06-06 23:40:33.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-06 23:40:33.061 [info] > git rev-parse --show-toplevel [42ms]
2025-06-06 23:40:33.069 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.079 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.088 [info] > git rev-parse --show-toplevel [6ms]
2025-06-06 23:40:33.098 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.468 [info] > git rev-parse --show-toplevel [219ms]
2025-06-06 23:40:33.483 [info] > git rev-parse --show-toplevel [11ms]
2025-06-06 23:40:33.485 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-06 23:40:33.533 [info] > git ls-files --stage -- server/openai.ts [40ms]
2025-06-06 23:40:33.623 [info] > git show --textconv :server/openai.ts [132ms]
2025-06-06 23:40:33.624 [info] > git cat-file -s 5b8861414cc7648782cc8e95edb91e0c6dc5d2bb [88ms]
2025-06-06 23:40:34.004 [info] > git blame --root --incremental 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- server/openai.ts [151ms]
2025-06-06 23:40:41.939 [info] > git config --get commit.template [2ms]
2025-06-06 23:40:41.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:40:41.950 [info] > git status -z -uall [3ms]
2025-06-06 23:40:41.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:40:46.961 [info] > git config --get commit.template [4ms]
2025-06-06 23:40:46.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:40:46.971 [info] > git status -z -uall [5ms]
2025-06-06 23:40:46.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:24.801 [info] > git config --get commit.template [6ms]
2025-06-06 23:43:24.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:24.813 [info] > git status -z -uall [7ms]
2025-06-06 23:43:24.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:24.823 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-06 23:43:24.829 [info] > git diff --name-status -z --diff-filter=ADMR 567a13994be8c9ce6824ff80fa7a3c5c719edfbf...refs/remotes/origin/main [1ms]
2025-06-06 23:43:29.834 [info] > git config --get commit.template [7ms]
2025-06-06 23:43:29.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:29.842 [info] > git status -z -uall [4ms]
2025-06-06 23:43:29.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:34.853 [info] > git config --get commit.template [4ms]
2025-06-06 23:43:34.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:34.861 [info] > git status -z -uall [3ms]
2025-06-06 23:43:34.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:43:39.872 [info] > git config --get commit.template [2ms]
2025-06-06 23:43:39.878 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:43:39.889 [info] > git status -z -uall [6ms]
2025-06-06 23:43:39.890 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:44.902 [info] > git config --get commit.template [5ms]
2025-06-06 23:43:44.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:43:44.912 [info] > git status -z -uall [5ms]
2025-06-06 23:43:44.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-06 23:43:53.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:43:53.877 [info] > git config --get commit.template [10ms]
2025-06-06 23:43:53.888 [info] > git status -z -uall [6ms]
2025-06-06 23:43:53.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:43:58.902 [info] > git config --get commit.template [1ms]
2025-06-06 23:43:58.913 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-06 23:43:58.932 [info] > git status -z -uall [6ms]
2025-06-06 23:43:58.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:05.071 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:05.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:05.080 [info] > git status -z -uall [4ms]
2025-06-06 23:44:05.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:10.094 [info] > git config --get commit.template [6ms]
2025-06-06 23:44:10.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:10.108 [info] > git status -z -uall [6ms]
2025-06-06 23:44:10.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:15.123 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:15.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:15.200 [info] > git status -z -uall [61ms]
2025-06-06 23:44:15.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-06 23:44:20.215 [info] > git config --get commit.template [7ms]
2025-06-06 23:44:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:20.226 [info] > git status -z -uall [6ms]
2025-06-06 23:44:20.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:25.238 [info] > git config --get commit.template [4ms]
2025-06-06 23:44:25.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:25.259 [info] > git status -z -uall [12ms]
2025-06-06 23:44:25.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:44:30.275 [info] > git config --get commit.template [6ms]
2025-06-06 23:44:30.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:44:30.289 [info] > git status -z -uall [6ms]
2025-06-06 23:44:30.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:35.300 [info] > git config --get commit.template [3ms]
2025-06-06 23:44:35.301 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:35.315 [info] > git status -z -uall [7ms]
2025-06-06 23:44:35.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:40.327 [info] > git config --get commit.template [4ms]
2025-06-06 23:44:40.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:44:40.339 [info] > git status -z -uall [4ms]
2025-06-06 23:44:40.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:45.354 [info] > git config --get commit.template [7ms]
2025-06-06 23:44:45.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:45.364 [info] > git status -z -uall [5ms]
2025-06-06 23:44:45.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:50.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:44:50.383 [info] > git config --get commit.template [10ms]
2025-06-06 23:44:50.404 [info] > git status -z -uall [12ms]
2025-06-06 23:44:50.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:55.418 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:55.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:55.434 [info] > git status -z -uall [8ms]
2025-06-06 23:44:55.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:00.452 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:00.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-06 23:45:00.487 [info] > git status -z -uall [15ms]
2025-06-06 23:45:00.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:05.500 [info] > git config --get commit.template [1ms]
2025-06-06 23:45:05.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:05.528 [info] > git status -z -uall [6ms]
2025-06-06 23:45:05.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:10.542 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:10.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:10.555 [info] > git status -z -uall [5ms]
2025-06-06 23:45:10.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:15.572 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:15.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:15.585 [info] > git status -z -uall [4ms]
2025-06-06 23:45:15.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:20.598 [info] > git config --get commit.template [3ms]
2025-06-06 23:45:20.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:20.607 [info] > git status -z -uall [5ms]
2025-06-06 23:45:20.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:25.619 [info] > git config --get commit.template [4ms]
2025-06-06 23:45:25.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:25.631 [info] > git status -z -uall [7ms]
2025-06-06 23:45:25.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:30.646 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:30.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:30.657 [info] > git status -z -uall [5ms]
2025-06-06 23:45:30.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:35.676 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:35.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:35.698 [info] > git status -z -uall [11ms]
2025-06-06 23:45:35.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:40.710 [info] > git config --get commit.template [2ms]
2025-06-06 23:45:40.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:40.731 [info] > git status -z -uall [4ms]
2025-06-06 23:45:40.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:45.742 [info] > git config --get commit.template [3ms]
2025-06-06 23:45:45.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:45.756 [info] > git status -z -uall [8ms]
2025-06-06 23:45:45.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:50.769 [info] > git config --get commit.template [5ms]
2025-06-06 23:45:50.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:50.782 [info] > git status -z -uall [8ms]
2025-06-06 23:45:50.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:55.798 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:55.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:55.809 [info] > git status -z -uall [4ms]
2025-06-06 23:45:55.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:00.822 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:00.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:00.838 [info] > git status -z -uall [7ms]
2025-06-06 23:46:00.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:05.853 [info] > git config --get commit.template [7ms]
2025-06-06 23:46:05.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:05.867 [info] > git status -z -uall [6ms]
2025-06-06 23:46:05.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:10.880 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:10.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:10.891 [info] > git status -z -uall [6ms]
2025-06-06 23:46:10.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:15.904 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:15.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:15.914 [info] > git status -z -uall [5ms]
2025-06-06 23:46:15.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:20.928 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:20.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:20.941 [info] > git status -z -uall [8ms]
2025-06-06 23:46:20.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:25.955 [info] > git config --get commit.template [6ms]
2025-06-06 23:46:25.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:25.966 [info] > git status -z -uall [3ms]
2025-06-06 23:46:25.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:30.977 [info] > git config --get commit.template [0ms]
2025-06-06 23:46:30.982 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:30.991 [info] > git status -z -uall [5ms]
2025-06-06 23:46:30.992 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:36.004 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:36.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:36.013 [info] > git status -z -uall [3ms]
2025-06-06 23:46:36.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:41.028 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:41.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:46:41.042 [info] > git status -z -uall [6ms]
2025-06-06 23:46:41.046 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:46:46.062 [info] > git config --get commit.template [9ms]
2025-06-06 23:46:46.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:46.078 [info] > git status -z -uall [7ms]
2025-06-06 23:46:46.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:47:38.441 [info] > git config --get commit.template [11ms]
2025-06-06 23:47:38.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:47:38.464 [info] > git status -z -uall [11ms]
2025-06-06 23:47:38.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-06 23:47:43.492 [info] > git config --get commit.template [6ms]
2025-06-06 23:47:43.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:47:43.506 [info] > git status -z -uall [8ms]
2025-06-06 23:47:43.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:47:48.522 [info] > git config --get commit.template [7ms]
2025-06-06 23:47:48.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:48.536 [info] > git status -z -uall [6ms]
2025-06-06 23:47:48.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:47:53.551 [info] > git config --get commit.template [6ms]
2025-06-06 23:47:53.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:53.563 [info] > git status -z -uall [5ms]
2025-06-06 23:47:53.564 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:47:58.573 [info] > git config --get commit.template [2ms]
2025-06-06 23:47:58.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:58.600 [info] > git status -z -uall [11ms]
2025-06-06 23:47:58.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:48:03.616 [info] > git config --get commit.template [5ms]
2025-06-06 23:48:03.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:48:03.628 [info] > git status -z -uall [4ms]
2025-06-06 23:48:03.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:48:08.679 [info] > git config --get commit.template [39ms]
2025-06-06 23:48:08.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:48:08.694 [info] > git status -z -uall [4ms]
2025-06-06 23:48:08.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:48:25.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:48:25.963 [info] > git config --get commit.template [10ms]
2025-06-06 23:48:25.978 [info] > git status -z -uall [9ms]
2025-06-06 23:48:25.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:48:30.995 [info] > git config --get commit.template [6ms]
2025-06-06 23:48:30.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:48:31.005 [info] > git status -z -uall [4ms]
2025-06-06 23:48:31.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:19.336 [info] > git config --get commit.template [5ms]
2025-06-06 23:49:19.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:49:19.355 [info] > git status -z -uall [11ms]
2025-06-06 23:49:19.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:24.375 [info] > git config --get commit.template [7ms]
2025-06-06 23:49:24.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:49:24.396 [info] > git status -z -uall [9ms]
2025-06-06 23:49:24.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:49:29.409 [info] > git config --get commit.template [1ms]
2025-06-06 23:49:29.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:49:29.428 [info] > git status -z -uall [6ms]
2025-06-06 23:49:29.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:34.452 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:49:34.453 [info] > git config --get commit.template [15ms]
2025-06-06 23:49:34.473 [info] > git status -z -uall [9ms]
2025-06-06 23:49:34.474 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:49:39.487 [info] > git config --get commit.template [4ms]
2025-06-06 23:49:39.488 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:49:39.499 [info] > git status -z -uall [7ms]
2025-06-06 23:49:39.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:44.530 [info] > git config --get commit.template [10ms]
2025-06-06 23:49:44.531 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:49:44.548 [info] > git status -z -uall [6ms]
2025-06-06 23:49:44.549 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:49.561 [info] > git config --get commit.template [4ms]
2025-06-06 23:49:49.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:49:49.570 [info] > git status -z -uall [4ms]
2025-06-06 23:49:49.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:54.598 [info] > git config --get commit.template [10ms]
2025-06-06 23:49:54.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:49:54.611 [info] > git status -z -uall [7ms]
2025-06-06 23:49:54.612 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:50:35.325 [info] > git config --get commit.template [6ms]
2025-06-06 23:50:35.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:50:35.342 [info] > git status -z -uall [8ms]
2025-06-06 23:50:35.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:50:57.176 [info] > git config --get commit.template [9ms]
2025-06-06 23:50:57.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:50:57.186 [info] > git status -z -uall [6ms]
2025-06-06 23:50:57.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:51:02.202 [info] > git config --get commit.template [5ms]
2025-06-06 23:51:02.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:51:02.214 [info] > git status -z -uall [5ms]
2025-06-06 23:51:02.215 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:51:07.233 [info] > git config --get commit.template [6ms]
2025-06-06 23:51:07.234 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:51:07.243 [info] > git status -z -uall [4ms]
2025-06-06 23:51:07.244 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:51:12.259 [info] > git config --get commit.template [1ms]
2025-06-06 23:51:12.276 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-06 23:51:12.470 [info] > git status -z -uall [175ms]
2025-06-06 23:51:12.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [148ms]
2025-06-06 23:51:17.481 [info] > git config --get commit.template [4ms]
2025-06-06 23:51:17.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:51:17.489 [info] > git status -z -uall [3ms]
2025-06-06 23:51:17.490 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:51:22.502 [info] > git config --get commit.template [3ms]
2025-06-06 23:51:22.504 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:51:22.516 [info] > git status -z -uall [5ms]
2025-06-06 23:51:22.517 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:54:17.183 [info] > git config --get commit.template [5ms]
2025-06-06 23:54:17.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:54:17.198 [info] > git status -z -uall [8ms]
2025-06-06 23:54:17.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:54:22.212 [info] > git config --get commit.template [0ms]
2025-06-06 23:54:22.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:54:22.233 [info] > git status -z -uall [8ms]
2025-06-06 23:54:22.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:54:27.250 [info] > git config --get commit.template [0ms]
2025-06-06 23:54:27.262 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:54:27.282 [info] > git status -z -uall [8ms]
2025-06-06 23:54:27.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:54:32.303 [info] > git config --get commit.template [9ms]
2025-06-06 23:54:32.304 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:54:32.314 [info] > git status -z -uall [5ms]
2025-06-06 23:54:32.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:54:37.325 [info] > git config --get commit.template [4ms]
2025-06-06 23:54:37.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:54:37.338 [info] > git status -z -uall [6ms]
2025-06-06 23:54:37.339 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:54:42.402 [info] > git config --get commit.template [56ms]
2025-06-06 23:54:42.409 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:54:42.423 [info] > git status -z -uall [9ms]
2025-06-06 23:54:42.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:54:47.440 [info] > git config --get commit.template [8ms]
2025-06-06 23:54:47.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:54:47.448 [info] > git status -z -uall [3ms]
2025-06-06 23:54:47.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:54:56.488 [info] > git config --get commit.template [4ms]
2025-06-06 23:54:56.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:54:56.496 [info] > git status -z -uall [4ms]
2025-06-06 23:54:56.497 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:55:01.508 [info] > git config --get commit.template [4ms]
2025-06-06 23:55:01.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-06 23:55:01.532 [info] > git status -z -uall [6ms]
2025-06-06 23:55:01.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:55:06.596 [info] > git config --get commit.template [56ms]
2025-06-06 23:55:06.603 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:55:06.616 [info] > git status -z -uall [9ms]
2025-06-06 23:55:06.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:55:11.629 [info] > git config --get commit.template [5ms]
2025-06-06 23:55:11.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:55:11.644 [info] > git status -z -uall [8ms]
2025-06-06 23:55:11.647 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:55:16.657 [info] > git config --get commit.template [0ms]
2025-06-06 23:55:16.667 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:55:16.684 [info] > git status -z -uall [8ms]
2025-06-06 23:55:16.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:55:21.699 [info] > git config --get commit.template [6ms]
2025-06-06 23:55:21.699 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:55:21.711 [info] > git status -z -uall [6ms]
2025-06-06 23:55:21.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:55:26.738 [info] > git config --get commit.template [14ms]
2025-06-06 23:55:26.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-06 23:55:26.778 [info] > git status -z -uall [9ms]
2025-06-06 23:55:26.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:55:35.949 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:55:35.949 [info] > git config --get commit.template [10ms]
2025-06-06 23:55:35.963 [info] > git status -z -uall [8ms]
2025-06-06 23:55:35.966 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:55:40.985 [info] > git config --get commit.template [0ms]
2025-06-06 23:55:40.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:55:41.010 [info] > git status -z -uall [5ms]
2025-06-06 23:55:41.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:55:46.030 [info] > git config --get commit.template [3ms]
2025-06-06 23:55:46.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:55:46.074 [info] > git status -z -uall [24ms]
2025-06-06 23:55:46.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-06 23:55:51.093 [info] > git config --get commit.template [3ms]
2025-06-06 23:55:51.093 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:55:51.100 [info] > git status -z -uall [3ms]
2025-06-06 23:55:51.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:55:56.111 [info] > git config --get commit.template [4ms]
2025-06-06 23:55:56.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:55:56.120 [info] > git status -z -uall [5ms]
2025-06-06 23:55:56.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:56:01.140 [info] > git config --get commit.template [8ms]
2025-06-06 23:56:01.141 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:56:01.150 [info] > git status -z -uall [6ms]
2025-06-06 23:56:01.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:56:06.165 [info] > git config --get commit.template [6ms]
2025-06-06 23:56:06.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:56:06.176 [info] > git status -z -uall [4ms]
2025-06-06 23:56:06.176 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:56:11.187 [info] > git config --get commit.template [4ms]
2025-06-06 23:56:11.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:56:11.195 [info] > git status -z -uall [4ms]
2025-06-06 23:56:11.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:56:25.167 [info] > git config --get commit.template [1ms]
2025-06-06 23:56:25.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:56:25.184 [info] > git status -z -uall [5ms]
2025-06-06 23:56:25.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:56:30.201 [info] > git config --get commit.template [4ms]
2025-06-06 23:56:30.202 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:56:30.211 [info] > git status -z -uall [5ms]
2025-06-06 23:56:30.211 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:56:35.227 [info] > git config --get commit.template [5ms]
2025-06-06 23:56:35.230 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:56:35.243 [info] > git status -z -uall [8ms]
2025-06-06 23:56:35.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:56:40.258 [info] > git config --get commit.template [6ms]
2025-06-06 23:56:40.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:56:40.273 [info] > git status -z -uall [10ms]
2025-06-06 23:56:40.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:56:45.294 [info] > git config --get commit.template [11ms]
2025-06-06 23:56:45.295 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:56:45.305 [info] > git status -z -uall [6ms]
2025-06-06 23:56:45.306 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:56:50.318 [info] > git config --get commit.template [4ms]
2025-06-06 23:56:50.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:56:50.327 [info] > git status -z -uall [4ms]
2025-06-06 23:56:50.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:56:55.341 [info] > git config --get commit.template [4ms]
2025-06-06 23:56:55.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:56:55.350 [info] > git status -z -uall [4ms]
2025-06-06 23:56:55.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:59:41.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:59:41.098 [info] > git config --get commit.template [8ms]
2025-06-06 23:59:41.105 [info] > git status -z -uall [3ms]
2025-06-06 23:59:41.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:59:46.118 [info] > git config --get commit.template [4ms]
2025-06-06 23:59:46.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:59:46.128 [info] > git status -z -uall [4ms]
2025-06-06 23:59:46.129 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:59:51.139 [info] > git config --get commit.template [3ms]
2025-06-06 23:59:51.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:59:51.146 [info] > git status -z -uall [3ms]
2025-06-06 23:59:51.147 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:59:56.157 [info] > git config --get commit.template [1ms]
2025-06-06 23:59:56.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:59:56.181 [info] > git status -z -uall [8ms]
2025-06-06 23:59:56.183 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:00:01.199 [info] > git config --get commit.template [5ms]
2025-06-07 00:00:01.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:00:01.212 [info] > git status -z -uall [6ms]
2025-06-07 00:00:01.213 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:00:06.226 [info] > git config --get commit.template [3ms]
2025-06-07 00:00:06.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:00:06.233 [info] > git status -z -uall [3ms]
2025-06-07 00:00:06.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:00:11.243 [info] > git config --get commit.template [1ms]
2025-06-07 00:00:11.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:00:11.259 [info] > git status -z -uall [5ms]
2025-06-07 00:00:11.259 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:00:16.269 [info] > git config --get commit.template [4ms]
2025-06-07 00:00:16.270 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:00:16.278 [info] > git status -z -uall [4ms]
2025-06-07 00:00:16.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:00:21.289 [info] > git config --get commit.template [4ms]
2025-06-07 00:00:21.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:00:21.297 [info] > git status -z -uall [4ms]
2025-06-07 00:00:21.298 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:00:26.309 [info] > git config --get commit.template [4ms]
2025-06-07 00:00:26.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:00:26.325 [info] > git status -z -uall [10ms]
2025-06-07 00:00:26.326 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:00:31.342 [info] > git config --get commit.template [7ms]
2025-06-07 00:00:31.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 00:00:31.357 [info] > git status -z -uall [7ms]
2025-06-07 00:00:31.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:01.770 [info] > git config --get commit.template [8ms]
2025-06-07 00:03:01.771 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:03:01.787 [info] > git status -z -uall [8ms]
2025-06-07 00:03:01.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:06.807 [info] > git config --get commit.template [7ms]
2025-06-07 00:03:06.810 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 00:03:06.825 [info] > git status -z -uall [6ms]
2025-06-07 00:03:06.827 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 00:03:11.855 [info] > git config --get commit.template [9ms]
2025-06-07 00:03:11.855 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:03:11.876 [info] > git status -z -uall [8ms]
2025-06-07 00:03:11.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 00:03:16.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 00:03:16.900 [info] > git config --get commit.template [10ms]
2025-06-07 00:03:16.915 [info] > git status -z -uall [9ms]
2025-06-07 00:03:16.916 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:03:24.540 [info] > git config --get commit.template [5ms]
2025-06-07 00:03:24.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-07 00:03:24.571 [info] > git status -z -uall [9ms]
2025-06-07 00:03:24.576 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-07 00:03:29.593 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-07 00:03:29.594 [info] > git config --get commit.template [8ms]
2025-06-07 00:03:29.615 [info] > git status -z -uall [7ms]
2025-06-07 00:03:29.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:34.631 [info] > git config --get commit.template [6ms]
2025-06-07 00:03:34.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:03:34.642 [info] > git status -z -uall [5ms]
2025-06-07 00:03:34.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:39.658 [info] > git config --get commit.template [7ms]
2025-06-07 00:03:39.659 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:03:39.675 [info] > git status -z -uall [6ms]
2025-06-07 00:03:39.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:03:44.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-07 00:03:44.694 [info] > git config --get commit.template [6ms]
2025-06-07 00:03:44.705 [info] > git status -z -uall [6ms]
2025-06-07 00:03:44.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:49.724 [info] > git config --get commit.template [6ms]
2025-06-07 00:03:49.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:03:49.740 [info] > git status -z -uall [9ms]
2025-06-07 00:03:49.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 00:03:54.755 [info] > git config --get commit.template [4ms]
2025-06-07 00:03:54.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:03:54.768 [info] > git status -z -uall [5ms]
2025-06-07 00:03:54.769 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:03:59.787 [info] > git config --get commit.template [8ms]
2025-06-07 00:03:59.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:03:59.800 [info] > git status -z -uall [7ms]
2025-06-07 00:03:59.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:04:04.816 [info] > git config --get commit.template [4ms]
2025-06-07 00:04:04.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:04.827 [info] > git status -z -uall [7ms]
2025-06-07 00:04:04.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:04:09.848 [info] > git config --get commit.template [9ms]
2025-06-07 00:04:09.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:04:09.863 [info] > git status -z -uall [8ms]
2025-06-07 00:04:09.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:04:14.875 [info] > git config --get commit.template [4ms]
2025-06-07 00:04:14.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:04:14.883 [info] > git status -z -uall [4ms]
2025-06-07 00:04:14.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:04:19.899 [info] > git config --get commit.template [5ms]
2025-06-07 00:04:19.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:19.913 [info] > git status -z -uall [8ms]
2025-06-07 00:04:19.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:04:24.928 [info] > git config --get commit.template [5ms]
2025-06-07 00:04:24.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:04:24.941 [info] > git status -z -uall [4ms]
2025-06-07 00:04:24.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-07 00:04:29.957 [info] > git config --get commit.template [6ms]
2025-06-07 00:04:29.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:29.970 [info] > git status -z -uall [7ms]
2025-06-07 00:04:29.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:04:34.992 [info] > git config --get commit.template [7ms]
2025-06-07 00:04:34.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-07 00:04:35.002 [info] > git status -z -uall [5ms]
2025-06-07 00:04:35.003 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-07 00:04:40.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:40.032 [info] > git config --get commit.template [13ms]
2025-06-07 00:04:40.052 [info] > git status -z -uall [11ms]
2025-06-07 00:04:40.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-07 00:04:45.067 [info] > git config --get commit.template [1ms]
2025-06-07 00:04:45.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:45.085 [info] > git status -z -uall [4ms]
2025-06-07 00:04:45.086 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-07 00:04:51.152 [info] > git config --get commit.template [4ms]
2025-06-07 00:04:51.152 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-07 00:04:51.159 [info] > git status -z -uall [3ms]
2025-06-07 00:04:51.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
