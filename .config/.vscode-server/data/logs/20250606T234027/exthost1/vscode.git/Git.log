2025-06-06 23:40:32.365 [info] [main] Log level: Info
2025-06-06 23:40:32.365 [info] [main] Validating found git in: "git"
2025-06-06 23:40:32.365 [info] [main] Using git "2.47.2" from "git"
2025-06-06 23:40:32.365 [info] [Model][doInitialScan] Initial repository scan started
2025-06-06 23:40:32.365 [info] > git rev-parse --show-toplevel [9ms]
2025-06-06 23:40:32.365 [info] > git rev-parse --git-dir --git-common-dir [313ms]
2025-06-06 23:40:32.365 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-06 23:40:32.365 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-06 23:40:32.365 [info] > git rev-parse --show-toplevel [62ms]
2025-06-06 23:40:32.365 [info] > git config --get commit.template [83ms]
2025-06-06 23:40:32.366 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [20ms]
2025-06-06 23:40:32.626 [info] > git status -z -uall [35ms]
2025-06-06 23:40:32.626 [info] > git rev-parse --show-toplevel [184ms]
2025-06-06 23:40:32.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [43ms]
2025-06-06 23:40:32.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-06-06 23:40:32.683 [info] > git rev-parse --show-toplevel [44ms]
2025-06-06 23:40:32.829 [info] > git config --get commit.template [158ms]
2025-06-06 23:40:32.895 [info] > git check-ignore -v -z --stdin [61ms]
2025-06-06 23:40:32.896 [info] > git rev-parse --show-toplevel [199ms]
2025-06-06 23:40:32.896 [info] > git config --get --local branch.main.vscode-merge-base [213ms]
2025-06-06 23:40:32.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-06-06 23:40:32.934 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [34ms]
2025-06-06 23:40:32.951 [info] > git rev-parse --show-toplevel [19ms]
2025-06-06 23:40:32.951 [info] > git merge-base refs/heads/main refs/remotes/origin/main [11ms]
2025-06-06 23:40:32.977 [info] > git diff --name-status -z --diff-filter=ADMR 914f82aa2133a3ab9e76799ac1b0b9bf2d295a95...refs/remotes/origin/main [20ms]
2025-06-06 23:40:33.002 [info] > git rev-parse --show-toplevel [37ms]
2025-06-06 23:40:33.056 [info] > git status -z -uall [70ms]
2025-06-06 23:40:33.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-06 23:40:33.061 [info] > git rev-parse --show-toplevel [42ms]
2025-06-06 23:40:33.069 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.079 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.088 [info] > git rev-parse --show-toplevel [6ms]
2025-06-06 23:40:33.098 [info] > git rev-parse --show-toplevel [4ms]
2025-06-06 23:40:33.468 [info] > git rev-parse --show-toplevel [219ms]
2025-06-06 23:40:33.483 [info] > git rev-parse --show-toplevel [11ms]
2025-06-06 23:40:33.485 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-06 23:40:33.533 [info] > git ls-files --stage -- server/openai.ts [40ms]
2025-06-06 23:40:33.623 [info] > git show --textconv :server/openai.ts [132ms]
2025-06-06 23:40:33.624 [info] > git cat-file -s 5b8861414cc7648782cc8e95edb91e0c6dc5d2bb [88ms]
2025-06-06 23:40:34.004 [info] > git blame --root --incremental 567a13994be8c9ce6824ff80fa7a3c5c719edfbf -- server/openai.ts [151ms]
2025-06-06 23:40:41.939 [info] > git config --get commit.template [2ms]
2025-06-06 23:40:41.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:40:41.950 [info] > git status -z -uall [3ms]
2025-06-06 23:40:41.951 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:40:46.961 [info] > git config --get commit.template [4ms]
2025-06-06 23:40:46.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:40:46.971 [info] > git status -z -uall [5ms]
2025-06-06 23:40:46.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:24.801 [info] > git config --get commit.template [6ms]
2025-06-06 23:43:24.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:24.813 [info] > git status -z -uall [7ms]
2025-06-06 23:43:24.814 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:24.823 [info] > git merge-base refs/heads/main refs/remotes/origin/main [1ms]
2025-06-06 23:43:24.829 [info] > git diff --name-status -z --diff-filter=ADMR 567a13994be8c9ce6824ff80fa7a3c5c719edfbf...refs/remotes/origin/main [1ms]
2025-06-06 23:43:29.834 [info] > git config --get commit.template [7ms]
2025-06-06 23:43:29.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:29.842 [info] > git status -z -uall [4ms]
2025-06-06 23:43:29.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:34.853 [info] > git config --get commit.template [4ms]
2025-06-06 23:43:34.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:43:34.861 [info] > git status -z -uall [3ms]
2025-06-06 23:43:34.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:43:39.872 [info] > git config --get commit.template [2ms]
2025-06-06 23:43:39.878 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:43:39.889 [info] > git status -z -uall [6ms]
2025-06-06 23:43:39.890 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:43:44.902 [info] > git config --get commit.template [5ms]
2025-06-06 23:43:44.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:43:44.912 [info] > git status -z -uall [5ms]
2025-06-06 23:43:44.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-06 23:43:53.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:43:53.877 [info] > git config --get commit.template [10ms]
2025-06-06 23:43:53.888 [info] > git status -z -uall [6ms]
2025-06-06 23:43:53.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:43:58.902 [info] > git config --get commit.template [1ms]
2025-06-06 23:43:58.913 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-06 23:43:58.932 [info] > git status -z -uall [6ms]
2025-06-06 23:43:58.933 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:05.071 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:05.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:05.080 [info] > git status -z -uall [4ms]
2025-06-06 23:44:05.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:10.094 [info] > git config --get commit.template [6ms]
2025-06-06 23:44:10.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:10.108 [info] > git status -z -uall [6ms]
2025-06-06 23:44:10.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:15.123 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:15.124 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:15.200 [info] > git status -z -uall [61ms]
2025-06-06 23:44:15.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [56ms]
2025-06-06 23:44:20.215 [info] > git config --get commit.template [7ms]
2025-06-06 23:44:20.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:20.226 [info] > git status -z -uall [6ms]
2025-06-06 23:44:20.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:25.238 [info] > git config --get commit.template [4ms]
2025-06-06 23:44:25.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:25.259 [info] > git status -z -uall [12ms]
2025-06-06 23:44:25.260 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:44:30.275 [info] > git config --get commit.template [6ms]
2025-06-06 23:44:30.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:44:30.289 [info] > git status -z -uall [6ms]
2025-06-06 23:44:30.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:44:35.300 [info] > git config --get commit.template [3ms]
2025-06-06 23:44:35.301 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:35.315 [info] > git status -z -uall [7ms]
2025-06-06 23:44:35.316 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:40.327 [info] > git config --get commit.template [4ms]
2025-06-06 23:44:40.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:44:40.339 [info] > git status -z -uall [4ms]
2025-06-06 23:44:40.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:45.354 [info] > git config --get commit.template [7ms]
2025-06-06 23:44:45.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:45.364 [info] > git status -z -uall [5ms]
2025-06-06 23:44:45.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:50.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-06 23:44:50.383 [info] > git config --get commit.template [10ms]
2025-06-06 23:44:50.404 [info] > git status -z -uall [12ms]
2025-06-06 23:44:50.405 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:44:55.418 [info] > git config --get commit.template [5ms]
2025-06-06 23:44:55.419 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:44:55.434 [info] > git status -z -uall [8ms]
2025-06-06 23:44:55.434 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:00.452 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:00.457 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-06 23:45:00.487 [info] > git status -z -uall [15ms]
2025-06-06 23:45:00.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:05.500 [info] > git config --get commit.template [1ms]
2025-06-06 23:45:05.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:05.528 [info] > git status -z -uall [6ms]
2025-06-06 23:45:05.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:10.542 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:10.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:10.555 [info] > git status -z -uall [5ms]
2025-06-06 23:45:10.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:15.572 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:15.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:15.585 [info] > git status -z -uall [4ms]
2025-06-06 23:45:15.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:20.598 [info] > git config --get commit.template [3ms]
2025-06-06 23:45:20.599 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:20.607 [info] > git status -z -uall [5ms]
2025-06-06 23:45:20.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:25.619 [info] > git config --get commit.template [4ms]
2025-06-06 23:45:25.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:25.631 [info] > git status -z -uall [7ms]
2025-06-06 23:45:25.632 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:30.646 [info] > git config --get commit.template [7ms]
2025-06-06 23:45:30.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:30.657 [info] > git status -z -uall [5ms]
2025-06-06 23:45:30.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:35.676 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:35.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:35.698 [info] > git status -z -uall [11ms]
2025-06-06 23:45:35.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:45:40.710 [info] > git config --get commit.template [2ms]
2025-06-06 23:45:40.716 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:45:40.731 [info] > git status -z -uall [4ms]
2025-06-06 23:45:40.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:45.742 [info] > git config --get commit.template [3ms]
2025-06-06 23:45:45.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:45.756 [info] > git status -z -uall [8ms]
2025-06-06 23:45:45.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:45:50.769 [info] > git config --get commit.template [5ms]
2025-06-06 23:45:50.770 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:50.782 [info] > git status -z -uall [8ms]
2025-06-06 23:45:50.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:45:55.798 [info] > git config --get commit.template [6ms]
2025-06-06 23:45:55.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:45:55.809 [info] > git status -z -uall [4ms]
2025-06-06 23:45:55.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:00.822 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:00.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:00.838 [info] > git status -z -uall [7ms]
2025-06-06 23:46:00.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:05.853 [info] > git config --get commit.template [7ms]
2025-06-06 23:46:05.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:05.867 [info] > git status -z -uall [6ms]
2025-06-06 23:46:05.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:10.880 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:10.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:10.891 [info] > git status -z -uall [6ms]
2025-06-06 23:46:10.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:15.904 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:15.905 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:15.914 [info] > git status -z -uall [5ms]
2025-06-06 23:46:15.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:20.928 [info] > git config --get commit.template [4ms]
2025-06-06 23:46:20.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:20.941 [info] > git status -z -uall [8ms]
2025-06-06 23:46:20.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:25.955 [info] > git config --get commit.template [6ms]
2025-06-06 23:46:25.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:25.966 [info] > git status -z -uall [3ms]
2025-06-06 23:46:25.967 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:30.977 [info] > git config --get commit.template [0ms]
2025-06-06 23:46:30.982 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:46:30.991 [info] > git status -z -uall [5ms]
2025-06-06 23:46:30.992 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:46:36.004 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:36.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:36.013 [info] > git status -z -uall [3ms]
2025-06-06 23:46:36.014 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:46:41.028 [info] > git config --get commit.template [5ms]
2025-06-06 23:46:41.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:46:41.042 [info] > git status -z -uall [6ms]
2025-06-06 23:46:41.046 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:46:46.062 [info] > git config --get commit.template [9ms]
2025-06-06 23:46:46.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:46:46.078 [info] > git status -z -uall [7ms]
2025-06-06 23:46:46.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:47:38.441 [info] > git config --get commit.template [11ms]
2025-06-06 23:47:38.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:47:38.464 [info] > git status -z -uall [11ms]
2025-06-06 23:47:38.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-06 23:47:43.492 [info] > git config --get commit.template [6ms]
2025-06-06 23:47:43.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:47:43.506 [info] > git status -z -uall [8ms]
2025-06-06 23:47:43.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:47:48.522 [info] > git config --get commit.template [7ms]
2025-06-06 23:47:48.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:48.536 [info] > git status -z -uall [6ms]
2025-06-06 23:47:48.537 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:47:53.551 [info] > git config --get commit.template [6ms]
2025-06-06 23:47:53.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:53.563 [info] > git status -z -uall [5ms]
2025-06-06 23:47:53.564 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-06 23:47:58.573 [info] > git config --get commit.template [2ms]
2025-06-06 23:47:58.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:47:58.600 [info] > git status -z -uall [11ms]
2025-06-06 23:47:58.604 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:48:03.616 [info] > git config --get commit.template [5ms]
2025-06-06 23:48:03.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:48:03.628 [info] > git status -z -uall [4ms]
2025-06-06 23:48:03.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:48:08.679 [info] > git config --get commit.template [39ms]
2025-06-06 23:48:08.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:48:08.694 [info] > git status -z -uall [4ms]
2025-06-06 23:48:08.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:48:25.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:48:25.963 [info] > git config --get commit.template [10ms]
2025-06-06 23:48:25.978 [info] > git status -z -uall [9ms]
2025-06-06 23:48:25.981 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-06 23:48:30.995 [info] > git config --get commit.template [6ms]
2025-06-06 23:48:30.996 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:48:31.005 [info] > git status -z -uall [4ms]
2025-06-06 23:48:31.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:19.336 [info] > git config --get commit.template [5ms]
2025-06-06 23:49:19.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-06 23:49:19.355 [info] > git status -z -uall [11ms]
2025-06-06 23:49:19.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-06 23:49:24.375 [info] > git config --get commit.template [7ms]
2025-06-06 23:49:24.382 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-06 23:49:24.396 [info] > git status -z -uall [9ms]
2025-06-06 23:49:24.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-06 23:49:29.409 [info] > git config --get commit.template [1ms]
2025-06-06 23:49:29.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-06 23:49:29.428 [info] > git status -z -uall [6ms]
2025-06-06 23:49:29.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
