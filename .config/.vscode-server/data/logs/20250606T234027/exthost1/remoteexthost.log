2025-06-06 23:40:29.499 [info] Extension host with pid 430 started
2025-06-06 23:40:29.499 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock'
2025-06-06 23:40:29.499 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-06 23:40:29.501 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': The pid 628 appears to be gone.
2025-06-06 23:40:29.501 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Deleting a stale lock.
2025-06-06 23:40:29.516 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/c2a2a4c2f07a9abc168edd922799525d/vscode.lock': Lock acquired.
2025-06-06 23:40:29.840 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-06 23:40:29.841 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-06 23:40:29.842 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-06 23:40:29.844 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescript'
2025-06-06 23:40:30.556 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-06 23:40:30.556 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-06 23:40:31.265 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-06 23:40:31.265 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-06 23:40:31.371 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-06 23:40:31.965 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-06 23:40:32.370 [info] Eager extensions activated
2025-06-06 23:40:32.370 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-06 23:40:32.371 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-06 23:40:37.769 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-06 23:40:38.697 [error] CodeExpectedError: cannot open vscode-userdata:/Users/<USER>/Library/Application%20Support/Code/User/keybindings.json. Detail: Unable to read file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json' (Error: Unable to resolve nonexistent file 'vscode-userdata:/Users/<USER>/Library/Application Support/Code/User/keybindings.json')
    at n_e.$tryOpenDocument (vscode-file://vscode-app/Applications/Visual%20Studio%20Code.app/Contents/Resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
2025-06-06 23:52:21.574 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:52:21.591 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:52:22.170 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:52:22.171 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:53:28.849 [info] ExtensionService#_doActivateExtension vscode.html-language-features, startup: false, activationEvent: 'onLanguage:html'
2025-06-06 23:53:55.363 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:53:55.368 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:53:55.831 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:53:55.833 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:20.779 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:20.782 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:21.233 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:21.234 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:49.224 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:49.227 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:49.692 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:54:49.693 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:55:01.654 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:55:01.658 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:55:02.169 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-06 23:55:02.170 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:03:58.208 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:03:58.214 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:03:58.756 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:03:58.757 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:04:15.192 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:04:15.194 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:04:15.667 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:04:15.668 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:14.581 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:14.591 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:15.105 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:15.107 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:35.428 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:35.431 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:35.978 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:05:35.980 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:25.192 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:25.223 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:25.795 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:25.798 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:39.534 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:39.536 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:40.061 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:40.062 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:55.275 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:55.278 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:55.699 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:07:55.700 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:08:18.097 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:08:18.099 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:08:18.666 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:08:18.667 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:14:56.448 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:14:56.453 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:14:56.929 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:14:56.930 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:15:16.701 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptModelChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:106201)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:15:16.703 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:15:17.196 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
2025-06-07 00:15:17.204 [error] ExtensionError: Error in extension augment.vscode-augment: FAILED to handle event
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:170:29209)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.C (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2443)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2660)
	at vV.$acceptDirtyStateChanged (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105732)
	at vV.$acceptModelSaved (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:145:105534)
	at f5.S (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162283)
	at f5.Q (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:162063)
	at f5.M (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:161152)
	at f5.L (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:160257)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:159054)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:225:3917)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at lo.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:9458)
	at J1.A (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:12574)
	at ed.value (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:10994)
	at D.B (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2373)
	at D.fire (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:27:2591)
	at ZE.acceptChunk (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7941)
	at file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:7227
	at Socket.t (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-258e40fedc6cb8edf399a463ce3a9d32e7e1f6f3/server/out/vs/workbench/api/node/extensionHostProcess.js:29:15251)
	at Socket.emit (node:events:524:28)
	at addChunk (node:internal/streams/readable:561:12)
	at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
	at Readable.push (node:internal/streams/readable:392:5)
	at Pipe.onStreamRead (node:internal/stream_base_commons:191:23)
