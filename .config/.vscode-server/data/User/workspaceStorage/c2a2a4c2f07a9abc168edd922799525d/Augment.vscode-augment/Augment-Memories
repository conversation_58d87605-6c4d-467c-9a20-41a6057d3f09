# User Preferences
- User prefers to use the latest OpenAI real-time model gpt-4o-realtime-preview-2025-06-03 for real-time conversations.
- User prefers debug logs to show relevant events (like VAD events, speech detection, etc.) rather than generic output for easier debugging of real-time conversation systems.

# Environment
- The user's workspace is running in a Replit environment.
- User accesses the server from port 80, not the local development port 5173, indicating a production or hosted environment setup.