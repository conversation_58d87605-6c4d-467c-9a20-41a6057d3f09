*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[23:40:27] 




[23:40:28] Extension host agent started.
[23:40:28] [<unknown>][fbcbd5ae][ManagementConnection] New connection established.
[23:40:28] [<unknown>][0b2e512c][ExtensionHostConnection] New connection established.
[23:40:28] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.472.1
[23:40:28] [<unknown>][0b2e512c][ExtensionHostConnection] <430> Launched Extension Host Process.
[23:40:28] ComputeTargetPlatform: linux-x64
[23:40:31] ComputeTargetPlatform: linux-x64
New EH opened, aborting shutdown
[23:45:28] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5173
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5173
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[00:53:45] Error: connect ECONNREFUSED 127.0.0.1:5173
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5173
}
