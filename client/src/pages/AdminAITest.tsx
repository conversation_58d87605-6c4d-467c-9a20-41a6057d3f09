import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Mic, MicOff, Download, RefreshCw, Zap, Volume2 } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";

// WebSocket URL using the same protocol and host as the current page
function getWebSocketUrl() {
  const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
  return `${protocol}//${window.location.host}/ws`;
}

const AdminAITest = () => {
  const { toast } = useToast();
  const [sessionActive, setSessionActive] = useState(false);
  const [transcript, setTranscript] = useState<Array<{ speaker: string; text: string; timestamp: Date }>>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState("disconnected");
  const [responseLog, setResponseLog] = useState<string[]>([]);
  const [selectedVoice, setSelectedVoice] = useState("shimmer");
  const [voiceSpeed, setVoiceSpeed] = useState(1.05);
  const [temperature, setTemperature] = useState(0.7);
  const [loadingStatus, setLoadingStatus] = useState("");
  const [summary, setSummary] = useState<string | null>(null);
  const [audioInputLevel, setAudioInputLevel] = useState(0);
  const [audioOutputPlaying, setAudioOutputPlaying] = useState(false);
  const [micDevices, setMicDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedMicDevice, setSelectedMicDevice] = useState<string>("");
  
  // Audio diagnostics state
  const [audioSupported, setAudioSupported] = useState(false);
  const [micPermissionGranted, setMicPermissionGranted] = useState(false);
  const [audioContextState, setAudioContextState] = useState<string>("suspended");
  const [audioStreamActive, setAudioStreamActive] = useState(false);
  
  // References
  const wsRef = useRef<WebSocket | null>(null);
  const transcriptEndRef = useRef<HTMLDivElement>(null);
  const testAudioRef = useRef<HTMLAudioElement | null>(null);
  
  // Audio context for microphone (16kHz for input)
  const audioContextRef = useRef<AudioContext | null>(null);
  const audioStreamRef = useRef<MediaStream | null>(null);
  const audioProcessorRef = useRef<ScriptProcessorNode | null>(null);
  const analyzerRef = useRef<AnalyserNode | null>(null);
  const levelCheckIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Client-side speech detection and manual VAD
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [isUserSpeaking, setIsUserSpeaking] = useState(false);
  const [speechDetectionEnabled, setSpeechDetectionEnabled] = useState(true);
  const speechBufferRef = useRef<string[]>([]);
  const speechStartTimeRef = useRef<number | null>(null);
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Audio playback context (24kHz for OpenAI output)
  const playbackContextRef = useRef<AudioContext | null>(null);
  const audioQueueRef = useRef<AudioBuffer[]>([]);
  const isPlayingRef = useRef(false);
  const nextPlayTimeRef = useRef(0);
  
  // Transcript accumulation
  const transcriptAccumulator = useRef<{[speaker: string]: string}>({});
  const transcriptTimeoutRef = useRef<{[speaker: string]: NodeJS.Timeout}>({});

  // Initialize WebSocket and start session
  const startInteractiveSession = async () => {
    setLoadingStatus("Connecting to server...");
    
    // Close existing connection if any
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.close();
    }
    
    try {
      // Initialize audio first to make sure we have microphone access
      const audioInitialized = await initializeAudioRecording();
      
      if (!audioInitialized) {
        setLoadingStatus("");
        toast({
          title: "Microphone access required",
          description: "Please allow microphone access to start the voice therapy session.",
          variant: "destructive",
        });
        return;
      }
      
      // Initialize playback audio context (required for hearing AI responses)
      const playbackReady = await initializePlaybackContext();
      if (!playbackReady) {
        addToLog('⚠️ Audio playback not ready - you may not hear AI responses');
        toast({
          title: "Audio Warning",
          description: "Audio playback may not work. Try 'Test Audio Output' first if you can't hear responses.",
          variant: "destructive",
        });
      }
      
      // Create WebSocket connection
      const ws = new WebSocket(getWebSocketUrl());
      wsRef.current = ws;

      ws.onopen = () => {
        console.log("WebSocket connection established");
        setConnectionStatus("connected");
        addToLog("WebSocket connection established");
        
        // Send initial configuration message with Realtime API enabled
        const startMessage = {
          type: "start",
          userId: "1",
          clientId: "1",
          useRealtimeAPI: true, // Enable OpenAI Realtime API
          mode: "realtime",
          behavior: {
            model: "gpt-4o-realtime-preview-2025-06-03",
            temperature: temperature,
            voice: {
              voice: selectedVoice,
              speed: voiceSpeed
            }
          },
          instructions: "You are Vale, an empathetic AI therapeutic assistant. Respond thoughtfully and supportively to help users process their emotions and thoughts. Keep responses concise and natural for voice conversation."
        };
        
        ws.send(JSON.stringify(startMessage));
        addToLog("🚀 STARTING SESSION: Real-time conversation with server-side VAD");
        addToLog(`🎯 CONFIG: Model=${startMessage.behavior.model}, Voice=${startMessage.behavior.voice.voice}, Temp=${startMessage.behavior.temperature}`);
        setLoadingStatus("Initializing session...");
      };

              ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("WebSocket message received:", data);

          // CRITICAL DEBUG: Log ALL messages to find missing ready
          addToLog(`📡 RECEIVED: ${data.type} ${data.message ? '- ' + data.message : ''}`);

          // Enhanced debug logging for real-time conversation events
          const logRealtimeEvent = (eventType: string, details?: string, isImportant = false) => {
            const prefix = isImportant ? "🔥" : "📡";
            const message = details ? `${prefix} ${eventType}: ${details}` : `${prefix} ${eventType}`;
            addToLog(message);
          };

          // Log all important OpenAI Realtime API events
          switch (data.type) {
            case "session.created":
              logRealtimeEvent("SESSION CREATED", `ID: ${data.session?.id || 'unknown'}`, true);
              break;
            case "session.updated":
              const vadConfig = data.session?.turn_detection;
              const vadType = vadConfig?.type || 'none';
              logRealtimeEvent("SESSION CONFIGURED", `VAD: ${vadType}, Model: ${data.session?.model || 'unknown'}`, true);
              break;
            case "ready":
              logRealtimeEvent("SESSION READY", "Real-time conversation active", true);
              break;
            case "input_audio_buffer.speech_started":
              logRealtimeEvent("🎤 SPEECH STARTED", "OpenAI detected you speaking", true);
              toast({
                title: "🎤 Speech Detected",
                description: "Keep talking, I'm listening...",
              });
              break;
            case "input_audio_buffer.speech_stopped":
              logRealtimeEvent("🔇 SPEECH STOPPED", "OpenAI detected silence", true);
              toast({
                title: "🔇 Processing Speech",
                description: "Transcribing and generating response...",
              });
              break;
            case "input_audio_buffer.committed":
              logRealtimeEvent("AUDIO COMMITTED", "Audio buffer sent for processing");
              break;
            case "conversation.item.created":
              const itemType = data.item?.type || 'unknown';
              logRealtimeEvent("CONVERSATION ITEM", `Created: ${itemType}`);
              break;
            case "conversation.item.input_audio_transcription.completed":
              const transcript = data.transcript || '';
              logRealtimeEvent("🎯 USER TRANSCRIBED", `"${transcript}"`, true);
              break;
            case "response.created":
              logRealtimeEvent("🤖 AI RESPONSE STARTED", "Generating response", true);
              break;
            case "response.output_item.added":
              logRealtimeEvent("RESPONSE ITEM", `Added: ${data.item?.type || 'unknown'}`);
              break;
            case "response.content_part.added":
              logRealtimeEvent("CONTENT PART", `Type: ${data.part?.type || 'unknown'}`);
              break;
            case "response.audio.delta":
              logRealtimeEvent("🔊 AUDIO CHUNK", `${data.delta?.length || 0} chars`);
              break;
            case "response.audio.done":
              logRealtimeEvent("🔊 AUDIO COMPLETE", "AI audio generation finished");
              break;
            case "response.audio_transcript.delta":
              // Don't log deltas to avoid spam
              break;
            case "response.audio_transcript.done":
              const aiTranscript = data.transcript || '';
              logRealtimeEvent("🤖 AI TRANSCRIBED", `"${aiTranscript}"`, true);
              break;
            case "response.done":
              logRealtimeEvent("✅ RESPONSE COMPLETE", "AI finished responding", true);
              break;
            case "error":
              logRealtimeEvent("❌ ERROR", data.message || 'Unknown error', true);
              break;
            case "rate_limits.updated":
              logRealtimeEvent("RATE LIMITS", `Remaining: ${JSON.stringify(data.rate_limits || {})}`);
              break;
            default:
              // Log other events with less emphasis
              logRealtimeEvent(`OTHER: ${data.type}`, data.message || '');
              break;
          }
          
          if (data.type === "ready") {
            setSessionActive(true);
            setLoadingStatus("");

            addToLog("✅ SESSION ACTIVE: Microphone streaming enabled, speak naturally");
            addToLog("💡 INSTRUCTIONS: Just speak - OpenAI will detect when you start/stop talking");
            setIsRecording(true);
          }

          // CRITICAL FIX: Also activate session on session.created or response.audio.delta
          if (data.type === "session.created" || data.type === "response.audio.delta") {
            if (!sessionActive) {
              addToLog("🔧 FORCE ACTIVATING SESSION: Detected working OpenAI connection");
              setSessionActive(true);
              setIsRecording(true);
              setLoadingStatus("");
            }
            
            toast({
              title: "Session started",
              description: "Voice recognition is active. Start speaking.",
            });
          } else if (data.type === "transcription") {
            // Add user speech to transcript
            if (data.text) {
              addToTranscript("You", data.text);
            }
          } else if (data.type === "conversation.item.input_audio_transcription.completed") {
            // OpenAI Realtime API - user speech transcription
            const text = data.transcript || "";
            if (text) {
              addToTranscript("You", text);
            }
          } else if (data.type === "input_audio_buffer.speech_started") {
            // OpenAI Realtime API - speech detection started
            addToLog("🎤 User speech started");
            toast({
              title: "Speech Detected",
              description: "Listening to your voice...",
            });
          } else if (data.type === "input_audio_buffer.speech_stopped") {
            // OpenAI Realtime API - speech detection stopped
            addToLog("🔇 User speech stopped");
            toast({
              title: "Processing Speech",
              description: "Transcribing and generating response...",
            });
          } else if (data.type === "response.audio_transcript.delta") {
            // Ignore fragments - we'll use the complete transcript from done event
            // This prevents fragmented display
          } else if (data.type === "response.audio_transcript.done") {
            // Use only the complete transcript from OpenAI
            if (data.transcript && data.transcript.trim()) {
              addToTranscript("AI", data.transcript.trim());
            }
          } else if (data.type === "response.audio.delta") {
            // OpenAI Realtime API - streaming audio from AI
            if (data.delta) {
              queueAudioChunk(data.delta);
            }
          } else if (data.type === "response.done") {
            // OpenAI Realtime API - response completed
            addToLog("Response completed");
          } else if (data.type === "assistant_response" || data.type === "chunk" || data.type === "text") {
            // Legacy API response handling
            const content = data.content || data.text || "";
            if (content) {
              addToTranscript("AI", content);
            }
          } else if (data.type === "audio_chunk") {
            // Handle audio playback (legacy)
            if (data.audioData) {
              queueAudioChunk(data.audioData);
            }
          } else if (data.type === "test_audio_response") {
            // Handle test audio response
            if (data.audioUrl) {
              playTestAudio(data.audioUrl);
            }
            setAudioOutputPlaying(false);
          } else if (data.type === "error") {
            toast({
              title: "Error",
              description: data.message || "Something went wrong",
              variant: "destructive",
            });
          } else if (data.type === "summary") {
            // Display conversation summary
            setSummary(data.content || "No summary available");
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };

      ws.onclose = () => {
        console.log("WebSocket connection closed");
        setConnectionStatus("disconnected");
        setSessionActive(false);
        setIsRecording(false);
        stopAudioRecording();
        addToLog("WebSocket connection closed");
      };

      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        setConnectionStatus("error");
        toast({
          title: "Connection error",
          description: "WebSocket connection failed",
          variant: "destructive",
        });
      };
    } catch (error) {
      console.error("Error starting session:", error);
      setLoadingStatus("");
      toast({
        title: "Session error",
        description: error instanceof Error ? error.message : "Failed to start session",
        variant: "destructive",
      });
    }
  };

  // Auto-scroll to bottom when transcript updates
  useEffect(() => {
    if (transcriptEndRef.current) {
      transcriptEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [transcript]);

  // Start microphone level monitoring on mount
  useEffect(() => {
    startMicrophoneLevelMonitoring();
    return () => {
      // Cleanup on unmount
      if (levelCheckIntervalRef.current) {
        clearInterval(levelCheckIntervalRef.current);
      }
    };
  }, []);

  // Initialize playback audio context for 24kHz OpenAI audio
  const initializePlaybackContext = async () => {
    try {
      console.log('🔊 Initializing playback context...');
      addToLog('🔊 Initializing audio playback context');
      
      if (!playbackContextRef.current) {
        // Create AudioContext with proper error handling
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        if (!AudioContextClass) {
          throw new Error('AudioContext not supported in this browser');
        }
        
        playbackContextRef.current = new AudioContextClass({
          sampleRate: 24000 // OpenAI Realtime API uses 24kHz
        });
        
        console.log('🔊 AudioContext created, state:', playbackContextRef.current.state);
        addToLog(`🔊 AudioContext created (${playbackContextRef.current.sampleRate}Hz, state: ${playbackContextRef.current.state})`);
      }
      
      // Resume if needed (required for user interaction)
      if (playbackContextRef.current.state === 'suspended') {
        console.log('🔊 Resuming suspended AudioContext...');
        addToLog('🔊 Resuming suspended AudioContext');
        await playbackContextRef.current.resume();
        console.log('🔊 AudioContext resumed, new state:', playbackContextRef.current.state);
        addToLog(`🔊 AudioContext resumed (state: ${playbackContextRef.current.state})`);
      }
      
      if (playbackContextRef.current.state === 'running') {
        console.log('✅ Playback context ready!');
        addToLog('✅ Playback context ready');
        return true;
      } else {
        console.warn('⚠️ AudioContext state:', playbackContextRef.current.state);
        addToLog(`⚠️ AudioContext state: ${playbackContextRef.current.state}`);
        return false;
      }
    } catch (error) {
      console.error('❌ Error initializing playback context:', error);
      addToLog(`❌ Playback context failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      toast({
        title: "Audio Playback Error",
        description: "Failed to initialize audio playback. Try clicking 'Test Audio Output' first.",
        variant: "destructive",
      });
      
      return false;
    }
  };

  // Add text to transcript display
  const addToTranscript = (speaker: string, text: string) => {
    setTranscript(prev => [...prev, {
      speaker,
      text,
      timestamp: new Date()
    }]);
  };

  // Accumulate transcript deltas and flush complete phrases
  const accumulateTranscript = (speaker: string, delta: string) => {
    // Clear any existing timeout for this speaker
    if (transcriptTimeoutRef.current[speaker]) {
      clearTimeout(transcriptTimeoutRef.current[speaker]);
    }

    // Accumulate the delta
    if (!transcriptAccumulator.current[speaker]) {
      transcriptAccumulator.current[speaker] = '';
    }
    transcriptAccumulator.current[speaker] += delta;

    // Set a longer timeout to ensure complete sentences are accumulated
    transcriptTimeoutRef.current[speaker] = setTimeout(() => {
      const accumulatedText = transcriptAccumulator.current[speaker];
      if (accumulatedText && accumulatedText.trim()) {
        addToTranscript(speaker, accumulatedText.trim());
        transcriptAccumulator.current[speaker] = '';
      }
    }, 1000); // 1 second delay to accumulate complete sentences
  };

  // Add message to response log
  const addToLog = (message: string) => {
    setResponseLog(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`].slice(-20));
  };
  
  // Queue audio chunk for proper sequential playback
  const queueAudioChunk = async (audioData: string) => {
    try {
      console.log('🔊 AUDIO CHUNK RECEIVED:', audioData.length, 'chars');
      addToLog(`🔊 AI AUDIO CHUNK: ${audioData.length} chars (${(audioData.length * 0.75 / 1024).toFixed(1)}KB)`);

      if (!playbackContextRef.current) {
        console.log('🔊 Initializing playback context...');
        await initializePlaybackContext();
      }

      if (!playbackContextRef.current) {
        console.error('❌ No playback context available');
        addToLog('❌ PLAYBACK ERROR: No audio context - cannot play AI voice');
        return;
      }

      // Convert base64 to ArrayBuffer (PCM16 format)
      const binaryString = atob(audioData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      
      console.log('🔊 Decoded audio data:', bytes.length, 'bytes');
      addToLog(`🔊 Decoded ${bytes.length} bytes of audio data`);
      
      // Create audio buffer for 24kHz PCM16
      const arrayBuffer = bytes.buffer.slice(bytes.byteOffset, bytes.byteOffset + bytes.byteLength);
      const audioBuffer = playbackContextRef.current.createBuffer(1, arrayBuffer.byteLength / 2, 24000);
      const channelData = audioBuffer.getChannelData(0);
      
      // Convert Int16 to Float32
      const int16Array = new Int16Array(arrayBuffer);
      for (let i = 0; i < int16Array.length; i++) {
        channelData[i] = int16Array[i] / 32768.0; // Convert to -1.0 to 1.0 range
      }
      
      console.log('🔊 Audio buffer created:', audioBuffer.length, 'samples,', audioBuffer.duration.toFixed(3), 'seconds');
      addToLog(`🔊 Buffer: ${audioBuffer.length} samples, ${audioBuffer.duration.toFixed(3)}s`);
      
      // Add to queue
      audioQueueRef.current.push(audioBuffer);
      console.log('🔊 Queue size:', audioQueueRef.current.length);
      addToLog(`📋 Audio queue size: ${audioQueueRef.current.length}`);
      
      // Start playing if not already playing
      if (!isPlayingRef.current) {
        console.log('🔊 Starting audio playback...');
        addToLog('▶️ AI SPEAKING: Starting voice playback');
        setAudioOutputPlaying(true);
        playNextInQueue();
      }
    } catch (error) {
      console.error('❌ Error queueing audio chunk:', error);
      addToLog(`❌ Audio error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Play the next audio buffer in the queue
  const playNextInQueue = () => {
    if (!playbackContextRef.current || audioQueueRef.current.length === 0) {
      isPlayingRef.current = false;
      setAudioOutputPlaying(false);
      console.log('🔊 Audio playback complete - queue empty');
      addToLog('⏹️ AI FINISHED SPEAKING: Voice playback complete');
      return;
    }

    isPlayingRef.current = true;
    const audioBuffer = audioQueueRef.current.shift()!;
    
    console.log('🔊 Playing audio buffer:', audioBuffer.duration.toFixed(3), 'seconds');
    addToLog(`▶️ Playing ${audioBuffer.duration.toFixed(3)}s of audio`);
    
    const source = playbackContextRef.current.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(playbackContextRef.current.destination);
    
    // Calculate when this should start relative to the audio timeline
    const currentTime = playbackContextRef.current.currentTime;
    const startTime = Math.max(currentTime, nextPlayTimeRef.current);
    
    source.start(startTime);
    
    // Calculate when the next audio should start
    nextPlayTimeRef.current = startTime + audioBuffer.duration;
    
    // When this source ends, play the next one
    source.onended = () => {
      console.log('🔊 Audio chunk finished, playing next...');
      playNextInQueue();
    };
  };
  
  // Play test audio from URL
  const playTestAudio = (audioUrl: string) => {
    try {
      if (!testAudioRef.current) {
        testAudioRef.current = new Audio();
      }
      
      testAudioRef.current.src = audioUrl;
      testAudioRef.current.play().catch(console.error);
      
      toast({
        title: "Test Audio Playing",
        description: "If you can hear this, your audio output is working correctly.",
      });
    } catch (error) {
      console.error('Error playing test audio:', error);
      toast({
        title: "Audio Playback Error",
        description: "Failed to play test audio.",
        variant: "destructive",
      });
    }
  };

  // Fetch available microphone devices
  const fetchMicrophoneDevices = async () => {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
        throw new Error("Media devices API not supported in this browser");
      }
      
      // Get permission first (required to see device labels)
      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      // List available devices
      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputDevices = devices.filter(device => device.kind === 'audioinput');
      
      console.log("Available audio input devices:", audioInputDevices);
      setMicDevices(audioInputDevices);
      
      // Select the default device if available
      if (audioInputDevices.length > 0) {
        setSelectedMicDevice(audioInputDevices[0].deviceId);
      }
      
      return audioInputDevices.length > 0;
    } catch (error) {
      console.error("Error fetching microphone devices:", error);
      toast({
        title: "Microphone Error",
        description: "Failed to detect microphone devices. Please ensure microphone permissions are granted.",
        variant: "destructive",
      });
      return false;
    }
  };
  
  // Test audio recording setup
  const testAudioRecording = async () => {
    addToLog("Testing audio recording setup...");
    
    try {
      // 1. Check API support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("getUserMedia API not supported");
      }
      addToLog("✓ getUserMedia API supported");
      setAudioSupported(true);
      
      // 2. Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 24000
        } 
      });
      addToLog("✓ Microphone permission granted");
      setMicPermissionGranted(true);
      setAudioStreamActive(true);
      
      // 3. Test audio context
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 24000
      });
      addToLog(`✓ Audio context created (sample rate: ${audioContext.sampleRate}Hz)`);
      setAudioContextState(audioContext.state);
      
      // 4. Test audio processing
      const source = audioContext.createMediaStreamSource(stream);
      const analyzer = audioContext.createAnalyser();
      analyzer.fftSize = 256;
      source.connect(analyzer);
      addToLog("✓ Audio analyzer connected");
      
      // 5. Test level detection for 3 seconds
      let maxLevel = 0;
      const bufferLength = analyzer.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      const testDuration = 3000; // 3 seconds
      const startTime = Date.now();
      
      const levelTest = () => {
        if (Date.now() - startTime < testDuration) {
          analyzer.getByteFrequencyData(dataArray);
          let sum = 0;
          for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i];
          }
          const level = sum / bufferLength / 255;
          maxLevel = Math.max(maxLevel, level);
          setAudioInputLevel(level);
          
          requestAnimationFrame(levelTest);
        } else {
          // Test complete
          setAudioInputLevel(0);
          stream.getTracks().forEach(track => track.stop());
          audioContext.close();
          setAudioStreamActive(false);
          
          addToLog(`✓ Audio test complete (max level: ${(maxLevel * 100).toFixed(1)}%)`);
          
          if (maxLevel > 0.01) {
            addToLog("✓ Audio input is working correctly");
            toast({
              title: "Audio Test Passed",
              description: `Microphone is working. Max level detected: ${(maxLevel * 100).toFixed(1)}%`,
            });
          } else {
            addToLog("⚠ Low audio input detected - check microphone");
            toast({
              title: "Audio Test Warning",
              description: "Low audio input detected. Please check your microphone volume.",
              variant: "destructive",
            });
          }
        }
      };
      
      addToLog("Testing audio input for 3 seconds - please speak...");
      toast({
        title: "Audio Test",
        description: "Testing microphone for 3 seconds. Please speak into your microphone.",
      });
      
      // Resume audio context if needed
      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }
      
      levelTest();
      
    } catch (error) {
      console.error("Audio test failed:", error);
      addToLog(`✗ Audio test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      toast({
        title: "Audio Test Failed",
        description: error instanceof Error ? error.message : "Failed to test audio recording",
        variant: "destructive",
      });
    }
  };

  // Test WebSocket connection
  const testWebSocketConnection = () => {
    addToLog("Testing WebSocket connection...");
    
    const testWs = new WebSocket(getWebSocketUrl());
    
    testWs.onopen = () => {
      addToLog("✓ WebSocket connection successful");
      testWs.send(JSON.stringify({ type: "ping" }));
    };
    
    testWs.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        addToLog(`✓ WebSocket message received: ${data.type}`);
      } catch (error) {
        addToLog(`✓ WebSocket raw message: ${event.data}`);
      }
      testWs.close();
    };
    
    testWs.onclose = () => {
      addToLog("✓ WebSocket test complete");
      toast({
        title: "WebSocket Test",
        description: "WebSocket connection test completed successfully",
      });
    };
    
    testWs.onerror = (error) => {
      addToLog("✗ WebSocket connection failed");
      toast({
        title: "WebSocket Test Failed",
        description: "Failed to connect to WebSocket server",
        variant: "destructive",
      });
    };
  };

  // Generate test audio from OpenAI
  const generateTestAudio = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      addToLog("🎤 Requesting test audio generation...");
      
      wsRef.current.send(JSON.stringify({
        type: "generate_test_audio",
        text: "Hello, this is a test of the voice system. I am speaking clearly so you can hear me and test the audio processing."
      }));
      
      toast({
        title: "Generating Test Audio",
        description: "OpenAI is generating test speech audio...",
      });
    } else {
      toast({
        title: "Not Connected",
        description: "Please start a session first",
        variant: "destructive",
      });
    }
  };

  // Test complete round-trip audio flow
  const testRoundTripAudio = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      addToLog("🔄 Starting round-trip audio test...");
      
      wsRef.current.send(JSON.stringify({
        type: "test_round_trip"
      }));
      
      toast({
        title: "Round-Trip Test",
        description: "Testing complete audio generation and processing flow...",
      });
    } else {
      toast({
        title: "Not Connected",
        description: "Please start a session first",
        variant: "destructive",
      });
    }
  };

  // Enhanced microphone level monitoring
  const startMicrophoneLevelMonitoring = (forceStart: boolean = false) => {
    addToLog(`Attempting to start microphone monitoring: recording=${isRecording}, forceStart=${forceStart}, analyzer=${!!analyzerRef.current}`);
    
    if (!analyzerRef.current) {
      addToLog("⚠ No analyzer available for level monitoring");
      return;
    }
    
    if (!isRecording && !forceStart) {
      addToLog("⚠ Not recording, skipping level monitoring");
      return;
    }
    
    addToLog("✓ Starting microphone level monitoring");
    const analyzer = analyzerRef.current;
    const bufferLength = analyzer.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateLevel = () => {
      // Check current states
      const currentlyRecording = isRecording;
      const hasAnalyzer = !!analyzerRef.current;
      const sessionIsActive = sessionActive;
      
      if (!currentlyRecording || !hasAnalyzer || !sessionIsActive) {
        setAudioInputLevel(0);
        addToLog(`Level monitoring stopped: recording=${currentlyRecording}, analyzer=${hasAnalyzer}, session=${sessionIsActive}`);
        return;
      }
      
      try {
        analyzer.getByteFrequencyData(dataArray);
        
        // Calculate average frequency amplitude for visualization
        let sum = 0;
        for (let i = 0; i < bufferLength; i++) {
          sum += dataArray[i];
        }
        const average = sum / bufferLength / 255;
        
        // Apply some amplification for better visibility
        const amplified = Math.min(1, average * 3);
        setAudioInputLevel(amplified);
        
        requestAnimationFrame(updateLevel);
      } catch (error) {
        console.error("Error updating audio level:", error);
        addToLog(`Error in level monitoring: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setAudioInputLevel(0);
      }
    };
    
    updateLevel();
  };

  // Enhanced audio recording initialization with comprehensive error handling
  const initializeAudioRecording = async () => {
    try {
      addToLog("🎤 Initializing audio recording...");

      // Check browser support
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("Media devices API not supported in this browser");
      }

      if (!window.AudioContext && !(window as any).webkitAudioContext) {
        throw new Error("Web Audio API not supported in this browser");
      }

      // Clean up any existing audio context
      if (audioContextRef.current) {
        try {
          await audioContextRef.current.close();
        } catch (e) {
          console.warn("Error closing existing audio context:", e);
        }
        audioContextRef.current = null;
      }

      // Request microphone access with optimal settings for speech recognition
      const constraints: MediaStreamConstraints = {
        audio: selectedMicDevice
          ? {
              deviceId: { exact: selectedMicDevice },
              echoCancellation: true,
              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI
              autoGainControl: true,
              sampleRate: { ideal: 24000, min: 16000, max: 48000 },
              channelCount: 1,  // Mono audio for efficiency
            }
          : {
              echoCancellation: true,
              noiseSuppression: false,  // Disable to preserve speech clarity for OpenAI
              autoGainControl: true,
              sampleRate: { ideal: 24000, min: 16000, max: 48000 },
              channelCount: 1,  // Mono audio for efficiency
            }
      };

      console.log("🎤 Using audio constraints:", constraints);
      addToLog(`🎤 Requesting microphone access${selectedMicDevice ? ` (device: ${selectedMicDevice.substr(0, 8)}...)` : ''}`);

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      audioStreamRef.current = stream;
      setAudioStreamActive(true);
      setMicPermissionGranted(true);
      addToLog("✅ Microphone access granted");

      // Log actual stream settings
      const audioTrack = stream.getAudioTracks()[0];
      if (audioTrack) {
        const settings = audioTrack.getSettings();
        addToLog(`🎤 Actual audio settings: ${settings.sampleRate}Hz, ${settings.channelCount} channel(s)`);
        console.log("🎤 Audio track settings:", settings);
      }

      // Initialize audio context with 24kHz for OpenAI Realtime API
      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      audioContextRef.current = new AudioContextClass({
        sampleRate: 24000, // Match OpenAI Realtime API sample rate exactly
      });

      const audioContext = audioContextRef.current;
      addToLog(`🎤 Audio context created: ${audioContext.sampleRate}Hz, state: ${audioContext.state}`);

      // Resume if suspended (required for user interaction)
      if (audioContext.state === 'suspended') {
        addToLog("🎤 Resuming suspended audio context...");
        await audioContext.resume();
      }
      setAudioContextState(audioContext.state);
      addToLog(`✅ Audio context ready: ${audioContext.state}`);

      const source = audioContext.createMediaStreamSource(stream);

      // Create analyzer for audio visualization
      const analyzer = audioContext.createAnalyser();
      analyzer.fftSize = 256;
      analyzer.smoothingTimeConstant = 0.8;
      analyzer.minDecibels = -90;
      analyzer.maxDecibels = -10;
      source.connect(analyzer);
      analyzerRef.current = analyzer;
      addToLog("✅ Audio analyzer connected");

      // Create processor for audio processing (using deprecated but reliable ScriptProcessorNode)
      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      audioProcessorRef.current = processor;
      
      // Set up audio processor for real-time audio streaming
      let audioFrameCount = 0;
      addToLog("✓ Setting up audio processor callback");
      
      processor.onaudioprocess = (e) => {
        try {
          audioFrameCount++;

          // Debug: Log that processor is working
          if (audioFrameCount <= 5) {
            console.log(`🔄 Audio processor callback #${audioFrameCount}`);
            addToLog(`🔄 Audio processor active: frame ${audioFrameCount}`);
          }

          // CRITICAL DEBUG: Log every 50 frames to confirm processor is running
          if (audioFrameCount % 50 === 0) {
            addToLog(`🔄 Audio processor still active: frame ${audioFrameCount}`);
          }

          // Check if we should be recording and sending audio
          const wsConnected = wsRef.current !== null && wsRef.current.readyState === WebSocket.OPEN;
          const currentlyRecording = wsConnected && sessionActive && isRecording;

          // Log the first few callbacks to confirm it's working
          if (audioFrameCount <= 3) {
            addToLog(`🎤 Audio callback ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}`);
          }

          // CRITICAL DEBUG: Log recording state every 50 frames
          if (audioFrameCount % 50 === 0) {
            addToLog(`🎤 Recording check frame ${audioFrameCount}: recording=${currentlyRecording}, session=${sessionActive}, ws=${wsConnected}, isRecording=${isRecording}`);
          }

          // Always update audio level for visual feedback, even if not recording
          if (analyzerRef.current) {
            try {
              const analyzer = analyzerRef.current;
              const bufferLength = analyzer.frequencyBinCount;
              const dataArray = new Uint8Array(bufferLength);
              analyzer.getByteFrequencyData(dataArray);

              let sum = 0;
              for (let i = 0; i < bufferLength; i++) {
                sum += dataArray[i];
              }
              const level = (sum / bufferLength / 255) * 3; // Amplify for visibility
              setAudioInputLevel(Math.min(1, level));

              // Debug: Log audio level for first few frames
              if (audioFrameCount <= 3) {
                addToLog(`🔊 Visual level update: ${(level * 100).toFixed(1)}% (analyzer working)`);
              }
            } catch (analyzerError) {
              console.warn("Error updating audio level:", analyzerError);
              if (audioFrameCount <= 3) {
                addToLog(`❌ Visual level error: ${analyzerError instanceof Error ? analyzerError.message : 'Unknown'}`);
              }
            }
          } else {
            // No analyzer available
            if (audioFrameCount <= 3) {
              addToLog(`❌ No analyzer available for visual level update`);
            }
          }

          if (currentlyRecording) {
            // Get audio data from the input channel
            const inputData = e.inputBuffer.getChannelData(0);

            if (!inputData || inputData.length === 0) {
              console.warn("No audio input data received");
              return;
            }

            // Calculate audio energy to detect if there's actual sound
            let energy = 0;
            for (let i = 0; i < inputData.length; i++) {
              energy += inputData[i] * inputData[i];
            }
            const rms = Math.sqrt(energy / inputData.length);

            // Convert Float32 audio to Int16 PCM for OpenAI with moderate gain
            const int16Data = new Int16Array(inputData.length);
            const gainMultiplier = 2.0; // Moderate amplification for clarity

            for (let i = 0; i < inputData.length; i++) {
              // Apply gain and convert float [-1,1] to int16 [-32768,32767] with proper clamping
              let sample = inputData[i] * gainMultiplier;
              sample = Math.max(-1, Math.min(1, sample)); // Clamp to prevent distortion
              int16Data[i] = sample < 0 ? Math.round(sample * 32768) : Math.round(sample * 32767);
            }

            // Convert Int16Array to base64 for OpenAI Realtime API
            const buffer = new ArrayBuffer(int16Data.length * 2);
            const view = new DataView(buffer);
            for (let i = 0; i < int16Data.length; i++) {
              view.setInt16(i * 2, int16Data[i], true); // little-endian
            }
            const uint8Array = new Uint8Array(buffer);
            const base64Audio = btoa(String.fromCharCode.apply(null, Array.from(uint8Array) as any));
          
            // HYBRID VAD: Use client-side detection to control audio streaming with silence gaps
            const amplifiedRms = rms * gainMultiplier;
            const speechThreshold = 0.01;
            const isSpeechDetected = amplifiedRms > speechThreshold;

            // Always send audio to OpenAI (including silence) for proper VAD processing
            try {
              wsRef.current!.send(JSON.stringify({
                type: 'input_audio_buffer.append',
                audio: base64Audio
              }));

              // Log successful audio transmission for first few frames
              if (audioFrameCount <= 3) {
                addToLog(`✅ Audio sent to OpenAI: ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);
              }

              // CRITICAL DEBUG: Log every 100 frames to confirm audio is being sent
              if (audioFrameCount % 100 === 0) {
                addToLog(`📤 Audio transmission active: frame ${audioFrameCount}, ${base64Audio.length} chars, RMS=${amplifiedRms.toFixed(4)}`);
              }
            } catch (wsError) {
              console.error("Error sending audio to WebSocket:", wsError);
              addToLog(`❌ Failed to send audio: ${wsError instanceof Error ? wsError.message : 'Unknown error'}`);
            }

            // Manage speech state with proper timing for visual feedback
            if (isSpeechDetected && !isUserSpeaking) {
              setIsUserSpeaking(true);
              addToLog("🎤 SPEECH DETECTED: Audio streaming to OpenAI VAD");

              // Clear any existing silence timeout
              if (silenceTimeoutRef.current) {
                clearTimeout(silenceTimeoutRef.current);
                silenceTimeoutRef.current = null;
              }
            } else if (!isSpeechDetected && isUserSpeaking) {
              // Start silence timer for visual feedback only
              if (!silenceTimeoutRef.current) {
                silenceTimeoutRef.current = setTimeout(() => {
                  setIsUserSpeaking(false);
                  addToLog("🔇 SILENCE DETECTED: Waiting for OpenAI VAD to process speech end");
                  silenceTimeoutRef.current = null;
                }, 1000); // 1 second delay for visual feedback
              }
            }

            // Log speech detection status periodically with more useful info
            if (audioFrameCount % 100 === 0) { // Reduced frequency to avoid spam
              const energyLevel = amplifiedRms > 0.1 ? "LOUD" : amplifiedRms > 0.01 ? "GOOD" : amplifiedRms > 0.001 ? "LOW" : "SILENT";
              const wsStatus = wsRef.current?.readyState === WebSocket.OPEN ? "CONNECTED" : "DISCONNECTED";
              addToLog(`📊 MIC STATUS: Energy=${energyLevel} (${amplifiedRms.toFixed(4)}), WS=${wsStatus}, Frames=${audioFrameCount}`);
            }

            // Log very first few audio frames to confirm processor is working
            if (audioFrameCount <= 10) {
              console.log(`🔄 Audio processor frame ${audioFrameCount}: RMS=${amplifiedRms.toFixed(4)}, threshold=${speechThreshold}, detected=${isSpeechDetected}`);
            }
          }
        } catch (processorError) {
          console.error("Error in audio processor:", processorError);
          if (audioFrameCount <= 5) {
            addToLog(`❌ Audio processor error: ${processorError instanceof Error ? processorError.message : 'Unknown error'}`);
          }
        }
      };
      
      // Connect the processor properly for audio processing
      source.connect(processor);

      // CRITICAL FIX: ScriptProcessorNode must be connected to destination to work
      // We create a gain node with zero volume to avoid feedback but enable processing
      const silentGain = audioContext.createGain();
      silentGain.gain.value = 0; // Silent output to prevent feedback
      processor.connect(silentGain);
      silentGain.connect(audioContext.destination);

      addToLog("✓ Audio processor connected with silent output (enables processing, prevents feedback)");
      
      // Store the analyzer for level monitoring
      analyzerRef.current = analyzer;
      addToLog("✓ Audio analyzer stored for monitoring");
      
      return true;
    } catch (error) {
      console.error("Error initializing audio recording:", error);
      addToLog(`✗ Audio initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      setAudioStreamActive(false);
      setMicPermissionGranted(false);
      
      toast({
        title: "Microphone error",
        description: error instanceof Error ? error.message : "Failed to access microphone",
        variant: "destructive",
      });
      
      return false;
    }
  };

  // Stop audio recording
  const stopAudioRecording = () => {
    setIsRecording(false);
    
    // Clean up audio processing
    if (audioProcessorRef.current) {
      try {
        audioProcessorRef.current.disconnect();
      } catch (e) {
        console.error("Error disconnecting audio processor:", e);
      }
      audioProcessorRef.current = null;
    }
    
    if (audioStreamRef.current) {
      audioStreamRef.current.getTracks().forEach(track => track.stop());
      audioStreamRef.current = null;
    }
  };
  
  // Test the voice output
  const testAudio = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      setAudioOutputPlaying(true);
      
      // Send test audio request
      wsRef.current.send(JSON.stringify({
        type: "test_audio",
        voice: selectedVoice,
        speed: voiceSpeed,
        text: "This is a test of the voice output. Can you hear this clearly?"
      }));
      
      addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);
      
      toast({
        title: "Testing Audio Output",
        description: `Playing test audio with ${selectedVoice} voice`,
      });
    } else {
      // Create a WebSocket connection for testing if one doesn't exist
      const ws = new WebSocket(getWebSocketUrl());
      wsRef.current = ws;
      
      ws.onopen = () => {
        setConnectionStatus("connected");
        addToLog("WebSocket connection established for audio test");
        
        // Send test audio request
        ws.send(JSON.stringify({
          type: "test_audio",
          voice: selectedVoice,
          speed: voiceSpeed,
          text: "This is a test of the voice output. Can you hear this clearly?"
        }));
        
        setAudioOutputPlaying(true);
        addToLog(`Sent test audio request (${selectedVoice}, speed: ${voiceSpeed})`);
      };
      
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("Test audio response:", data);
          
          if (data.type === "test_audio_response") {
            // Play the audio
            const audioElement = new Audio(data.audioUrl);
            testAudioRef.current = audioElement;
            
            audioElement.onplay = () => {
              addToLog(`Playing test audio (${selectedVoice})`);
            };
            
            audioElement.onended = () => {
              addToLog(`Test audio playback completed`);
              setAudioOutputPlaying(false);
              ws.close();
            };
            
            audioElement.onerror = (e) => {
              console.error("Audio playback error:", e);
              setAudioOutputPlaying(false);
              addToLog(`Error playing test audio`);
              ws.close();
            };
            
            // Start playback
            audioElement.play()
              .catch(err => {
                console.error("Audio play error:", err);
                setAudioOutputPlaying(false);
                addToLog(`Play error: ${err.message}`);
              });
          }
        } catch (error) {
          console.error("Error parsing WebSocket message:", error);
        }
      };
      
      ws.onerror = (error) => {
        console.error("WebSocket error during audio test:", error);
        setAudioOutputPlaying(false);
        toast({
          title: "Connection Error",
          description: "Failed to connect to server for audio test",
          variant: "destructive",
        });
      };
    }
  };

  // End the session and get a summary
  const endSession = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        type: "conversationEnded"
      }));
      
      addToLog("Ended session");
      
      // Stop recording
      stopAudioRecording();
      
      // For Realtime API sessions, summary generation is not yet implemented
      // Only request summary for legacy sessions
      // Note: We're using Realtime API mode, so skip summary for now
      
      toast({
        title: "Session ended",
        description: "Session completed successfully.",
      });
    }
  };
  
  // Export transcript as text
  const exportTranscript = () => {
    const textContent = transcript.map(t => 
      `[${t.timestamp.toLocaleTimeString()}] ${t.speaker}: ${t.text}`
    ).join('\n\n');
    
    // Add summary if available
    const fullContent = summary 
      ? `${textContent}\n\n--------\nSUMMARY:\n${summary}` 
      : textContent;
    
    // Create download link
    const blob = new Blob([fullContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `voice-therapy-transcript-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Transcript exported",
      description: "The conversation transcript has been downloaded",
    });
  };
  
  // Initialize diagnostics on component mount
  useEffect(() => {
    // Check audio support
    const checkAudioSupport = () => {
      const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
      const hasAudioContext = !!(window.AudioContext || (window as any).webkitAudioContext);
      
      setAudioSupported(hasGetUserMedia && hasAudioContext);
      addToLog(`Audio API support: getUserMedia=${hasGetUserMedia}, AudioContext=${hasAudioContext}`);
    };
    
    checkAudioSupport();
    
    // Fetch available microphone devices when the component mounts
    fetchMicrophoneDevices();
    
    return () => {
      // Stop recording if active
      stopAudioRecording();
      
      // Close WebSocket connection
      if (wsRef.current) {
        wsRef.current.close();
      }
      
      // Clear intervals
      if (levelCheckIntervalRef.current) {
        clearInterval(levelCheckIntervalRef.current);
      }
    };
  }, []);
  
  // Start/stop microphone level monitoring when recording state changes
  useEffect(() => {
    addToLog(`Recording state changed: isRecording=${isRecording}, sessionActive=${sessionActive}, analyzer=${!!analyzerRef.current}`);
    
    if (isRecording && sessionActive) {
      addToLog("✓ Starting microphone level monitoring due to recording state change");
      // Use multiple attempts with increasing delays to ensure state has updated
      setTimeout(() => {
        startMicrophoneLevelMonitoring(true); // Force start even if state check fails
      }, 100);
    } else {
      setAudioInputLevel(0);
      if (!isRecording) {
        addToLog("Recording stopped - clearing audio input level");
      }
    }
  }, [isRecording, sessionActive]);

  // Test debug speech detection
  const testSpeechDetection = () => {
    if (!isRecording || !sessionActive) {
      toast({
        title: "Start Session First",
        description: "Please start an interactive session before testing speech detection.",
        variant: "destructive",
      });
      return;
    }

    addToLog("🔍 SPEECH DETECTION TEST STARTED - Say 'Hello' loudly into your microphone");
    toast({
      title: "🔍 Speech Detection Test",
      description: "Say 'Hello' loudly into your microphone. Check the debug log for detection status.",
    });

    // Log speech detection status every second for 10 seconds
    let testCount = 0;
    const testInterval = setInterval(() => {
      testCount++;
      const bufferSize = speechBufferRef.current.length;
      const speaking = isUserSpeaking;
      const speechTime = speechStartTimeRef.current ? Date.now() - speechStartTimeRef.current : 0;

      addToLog(`🔍 Test ${testCount}/10: Speaking=${speaking}, Buffer=${bufferSize} chunks, SpeechTime=${speechTime}ms`);

      if (testCount >= 10) {
        clearInterval(testInterval);
        addToLog("🔍 SPEECH DETECTION TEST COMPLETED");
        toast({
          title: "Test Complete",
          description: "Check the debug log to see if your speech was detected. Look for 'Speaking=true' when you spoke.",
        });
      }
    }, 1000);
  };

  // Comprehensive voice system test (mimics the successful backend test)
  const testVoiceSystemComprehensive = () => {
    if (!sessionActive) {
      toast({
        title: "Start Session First",
        description: "Please start an interactive session before running the comprehensive test.",
        variant: "destructive",
      });
      return;
    }

    addToLog("🚀 COMPREHENSIVE VOICE SYSTEM TEST STARTED");
    toast({
      title: "🚀 Comprehensive Test",
      description: "Running full voice system test. Check the debug log for detailed results.",
    });

    let testResults = {
      sessionReady: sessionActive,
      microphoneActive: isRecording,
      audioContextReady: audioContextRef.current?.state === 'running',
      websocketConnected: wsRef.current?.readyState === WebSocket.OPEN,
      audioProcessorActive: !!audioProcessorRef.current,
      analyzerActive: !!analyzerRef.current
    };

    addToLog("📊 SYSTEM STATUS CHECK:");
    addToLog(`  Session Ready: ${testResults.sessionReady ? '✅' : '❌'}`);
    addToLog(`  Microphone Active: ${testResults.microphoneActive ? '✅' : '❌'}`);
    addToLog(`  Audio Context: ${testResults.audioContextReady ? '✅' : '❌'} (${audioContextRef.current?.state || 'none'})`);
    addToLog(`  WebSocket: ${testResults.websocketConnected ? '✅' : '❌'} (${wsRef.current?.readyState || 'none'})`);
    addToLog(`  Audio Processor: ${testResults.audioProcessorActive ? '✅' : '❌'}`);
    addToLog(`  Audio Analyzer: ${testResults.analyzerActive ? '✅' : '❌'}`);
    addToLog(`  Current Audio Level: ${(audioInputLevel * 100).toFixed(1)}%`);
    addToLog(`  Audio Stream: ${audioStreamRef.current ? '✅ Active' : '❌ None'}`);

    // Additional debugging for audio pipeline
    if (audioStreamRef.current) {
      const tracks = audioStreamRef.current.getAudioTracks();
      addToLog(`  Audio Tracks: ${tracks.length} (${tracks.map(t => t.label || 'Unknown').join(', ')})`);
      if (tracks.length > 0) {
        const settings = tracks[0].getSettings();
        addToLog(`  Track Settings: ${settings.sampleRate}Hz, ${settings.channelCount}ch`);
      }
    }

    // Test audio level detection using the same method as the visual indicator
    if (analyzerRef.current) {
      addToLog("🎤 Testing audio level detection for 5 seconds...");
      let maxLevel = 0;
      let levelTestCount = 0;

      const levelTestInterval = setInterval(() => {
        levelTestCount++;

        // Use the same audio analysis method as the visual indicator
        let currentLevel = 0;
        try {
          const analyzer = analyzerRef.current;
          if (analyzer) {
            const bufferLength = analyzer.frequencyBinCount;
            const dataArray = new Uint8Array(bufferLength);
            analyzer.getByteFrequencyData(dataArray);

            let sum = 0;
            for (let i = 0; i < bufferLength; i++) {
              sum += dataArray[i];
            }
            currentLevel = (sum / bufferLength / 255) * 3; // Same calculation as visual indicator
            currentLevel = Math.min(1, currentLevel);
          }
        } catch (error) {
          console.warn("Error reading audio level:", error);
          currentLevel = audioInputLevel; // Fallback to state variable
        }

        maxLevel = Math.max(maxLevel, currentLevel);

        if (levelTestCount <= 5) {
          addToLog(`  Level test ${levelTestCount}/5: ${(currentLevel * 100).toFixed(1)}% (analyzer=${!!analyzerRef.current})`);
        }

        if (levelTestCount >= 5) {
          clearInterval(levelTestInterval);
          addToLog(`✅ Audio level test complete. Max level: ${(maxLevel * 100).toFixed(1)}%`);

          if (maxLevel > 0.05) {
            addToLog("✅ Audio input is working correctly");
          } else {
            addToLog("⚠️ Low audio input detected - check microphone volume or analyzer connection");
            addToLog(`🔍 Debug: audioInputLevel=${(audioInputLevel * 100).toFixed(1)}%, analyzer=${!!analyzerRef.current}`);
          }
        }
      }, 1000);
    } else {
      addToLog("❌ No audio analyzer available - microphone may not be properly initialized");
    }

    // Generate summary after 6 seconds
    setTimeout(() => {
      const allSystemsGo = Object.values(testResults).every(result => result);

      addToLog("🔍 COMPREHENSIVE TEST RESULTS:");
      if (allSystemsGo) {
        addToLog("✅ ALL SYSTEMS OPERATIONAL - Voice conversation should work!");
        toast({
          title: "✅ Test Passed",
          description: "All systems are working correctly. Voice conversation should function properly.",
        });
      } else {
        addToLog("❌ SOME SYSTEMS NOT READY - Check failed components above");
        toast({
          title: "⚠️ Test Issues",
          description: "Some components are not working correctly. Check the debug log for details.",
          variant: "destructive",
        });
      }
    }, 6000);
  };

  // Real-time audio flow debugging
  const testRealTimeAudioFlow = () => {
    if (!sessionActive) {
      toast({
        title: "Start Session First",
        description: "Please start an interactive session before testing audio flow.",
        variant: "destructive",
      });
      return;
    }

    addToLog("🔍 REAL-TIME AUDIO FLOW DEBUG STARTED");
    addToLog("📢 SPEAK NOW - monitoring audio processing in real-time");

    toast({
      title: "🔍 Real-Time Debug",
      description: "SPEAK NOW! Monitoring audio processing for 10 seconds.",
    });

    let debugCount = 0;
    let lastAudioFrameCount = 0;
    let lastTransmissionCount = 0;
    let audioTransmissionCount = 0;

    // Monitor WebSocket messages for audio responses
    const originalOnMessage = wsRef.current?.onmessage;
    let speechStartReceived = false;
    let transcriptionReceived = false;
    let aiResponseReceived = false;

    if (wsRef.current) {
      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          switch (data.type) {
            case 'input_audio_buffer.speech_started':
              speechStartReceived = true;
              addToLog("🎤 REAL-TIME: OpenAI detected speech start!");
              break;
            case 'input_audio_buffer.speech_stopped':
              addToLog("🔇 REAL-TIME: OpenAI detected speech stop!");
              break;
            case 'conversation.item.input_audio_transcription.completed':
              transcriptionReceived = true;
              addToLog(`📝 REAL-TIME: Transcription received: "${data.transcript}"`);
              break;
            case 'response.audio_transcript.done':
              aiResponseReceived = true;
              addToLog(`🤖 REAL-TIME: AI response: "${data.transcript}"`);
              break;
          }
        } catch (error) {
          // Ignore parse errors
        }

        // Call original handler
        if (originalOnMessage) {
          originalOnMessage(event);
        }
      };
    }

    const debugInterval = setInterval(() => {
      debugCount++;

      // Check if audio processor is running
      const currentFrameCount = audioFrameCount;
      const framesProcessed = currentFrameCount - lastAudioFrameCount;
      lastAudioFrameCount = currentFrameCount;

      // Check current audio level
      let currentLevel = 0;
      if (analyzerRef.current) {
        try {
          const analyzer = analyzerRef.current;
          const bufferLength = analyzer.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);
          analyzer.getByteFrequencyData(dataArray);

          let sum = 0;
          for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i];
          }
          currentLevel = (sum / bufferLength / 255) * 100;
        } catch (error) {
          // Ignore
        }
      }

      addToLog(`🔍 Debug ${debugCount}/10: Frames=${framesProcessed}/sec, Level=${currentLevel.toFixed(1)}%, Speech=${speechStartReceived}, Transcript=${transcriptionReceived}, AI=${aiResponseReceived}`);

      if (debugCount >= 10) {
        clearInterval(debugInterval);

        // Restore original message handler
        if (wsRef.current && originalOnMessage) {
          wsRef.current.onmessage = originalOnMessage;
        }

        addToLog("🔍 REAL-TIME DEBUG COMPLETE");
        addToLog("📊 RESULTS:");
        addToLog(`  Audio Processor: ${lastAudioFrameCount > 0 ? '✅ Running' : '❌ Not running'}`);
        addToLog(`  Speech Detection: ${speechStartReceived ? '✅ Working' : '❌ Not detected'}`);
        addToLog(`  Transcription: ${transcriptionReceived ? '✅ Working' : '❌ Not received'}`);
        addToLog(`  AI Response: ${aiResponseReceived ? '✅ Working' : '❌ Not received'}`);

        if (!speechStartReceived && lastAudioFrameCount > 0) {
          addToLog("⚠️ ISSUE: Audio processor running but OpenAI not detecting speech");
          addToLog("💡 TRY: Speak louder or check microphone sensitivity");
        } else if (!lastAudioFrameCount) {
          addToLog("❌ ISSUE: Audio processor not running - check audio context connection");
        }

        toast({
          title: "Debug Complete",
          description: "Check the debug log for detailed audio flow analysis.",
        });
      }
    }, 1000);
  };

  // Direct audio test - simpler approach
  const testDirectAudioSend = () => {
    if (!sessionActive || !wsRef.current) {
      toast({
        title: "Start Session First",
        description: "Please start an interactive session before testing direct audio.",
        variant: "destructive",
      });
      return;
    }

    addToLog("🎯 DIRECT AUDIO TEST - Checking if audio reaches OpenAI");

    // Generate a simple test audio chunk (like our backend test)
    const generateTestAudio = () => {
      const sampleRate = 24000;
      const duration = 0.1; // 100ms
      const samples = Math.floor(sampleRate * duration);
      const buffer = new ArrayBuffer(samples * 2);
      const view = new DataView(buffer);

      for (let i = 0; i < samples; i++) {
        const t = i / sampleRate;
        const frequency = 440; // A4 note
        const sample = Math.sin(2 * Math.PI * frequency * t) * 0.5;
        const int16Sample = Math.round(sample * 32767);
        view.setInt16(i * 2, int16Sample, true);
      }

      const uint8Array = new Uint8Array(buffer);
      return btoa(String.fromCharCode.apply(null, Array.from(uint8Array)));
    };

    // Send 5 test audio chunks
    let chunkCount = 0;
    const sendTestChunk = () => {
      if (chunkCount >= 5) {
        addToLog("🎯 Direct audio test complete - sent 5 chunks");
        return;
      }

      const testAudio = generateTestAudio();

      try {
        wsRef.current!.send(JSON.stringify({
          type: 'input_audio_buffer.append',
          audio: testAudio
        }));

        chunkCount++;
        addToLog(`🎯 Sent test audio chunk ${chunkCount}/5: ${testAudio.length} chars`);

        setTimeout(sendTestChunk, 200); // Send every 200ms
      } catch (error) {
        addToLog(`❌ Failed to send test audio: ${error instanceof Error ? error.message : 'Unknown'}`);
      }
    };

    // Monitor for OpenAI responses
    const originalOnMessage = wsRef.current.onmessage;
    let responseCount = 0;

    wsRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type.includes('input_audio_buffer') || data.type.includes('speech') || data.type.includes('response')) {
          responseCount++;
          addToLog(`🎯 OpenAI response ${responseCount}: ${data.type}`);
        }
      } catch (error) {
        // Ignore parse errors
      }

      // Call original handler
      if (originalOnMessage) {
        originalOnMessage(event);
      }
    };

    // Restore original handler after 10 seconds
    setTimeout(() => {
      if (wsRef.current && originalOnMessage) {
        wsRef.current.onmessage = originalOnMessage;
      }
      addToLog(`🎯 Direct test complete. OpenAI responses received: ${responseCount}`);

      if (responseCount === 0) {
        addToLog("❌ ISSUE: No responses from OpenAI - check WebSocket connection or API");
      } else {
        addToLog("✅ OpenAI is receiving and processing audio data");
      }
    }, 10000);

    sendTestChunk();

    toast({
      title: "🎯 Direct Audio Test",
      description: "Sending test audio directly to OpenAI. Check debug log for results.",
    });
  };

  return (
    <div className="container mx-auto py-6 max-w-5xl">
      <h1 className="text-3xl font-bold mb-6">AI Voice Therapy Assistant</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="mb-6">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle>Real-Time Voice Interaction</CardTitle>
              <div className="flex items-center space-x-2">
                <span className={`inline-block w-3 h-3 rounded-full ${
                  connectionStatus === "connected" ? "bg-green-500" : 
                  connectionStatus === "error" ? "bg-red-500" : "bg-yellow-500"
                }`}></span>
                <span className="text-sm text-gray-500">
                  {connectionStatus === "connected" ? "Connected" : 
                   connectionStatus === "error" ? "Error" : "Disconnected"}
                </span>
              </div>
            </CardHeader>
            <CardContent>
              {!sessionActive ? (
                <div className="flex flex-col items-center justify-center py-10">
                  {loadingStatus ? (
                    <div className="flex flex-col items-center gap-3">
                      <RefreshCw className="animate-spin h-8 w-8 text-primary" />
                      <p>{loadingStatus}</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <p className="text-lg mb-6">Start an interactive real-time voice therapy session.</p>
                                                          <div className="text-center">
                    <Button onClick={startInteractiveSession} className="px-6" size="lg">
                      <Zap className="mr-2 h-5 w-5" />
                      Start Interactive Session
                    </Button>
                    <p className="text-sm text-gray-500 mt-4">
                      Microphone access is required for this feature.
                    </p>
                  </div>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">Live Transcript</h3>
                    <div className="flex items-center space-x-4">
                      {isRecording ? (
                        <div className="flex items-center text-green-500">
                          <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2 animate-pulse"></span>
                          <span className="text-sm">Recording</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-red-500">
                          <span className="inline-block w-2 h-2 rounded-full bg-red-500 mr-2"></span>
                          <span className="text-sm">Paused</span>
                        </div>
                      )}
                      {audioOutputPlaying && (
                        <div className="flex items-center text-blue-500">
                          <Volume2 className="h-4 w-4 mr-1 animate-pulse" />
                          <span className="text-sm">AI Speaking</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <ScrollArea className="h-80 border rounded-md p-4">
                    {transcript.length === 0 ? (
                      <div className="text-center text-gray-500 py-10">
                        <p>The conversation transcript will appear here</p>
                        <p className="text-sm mt-2">Start speaking to begin the session</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {transcript.map((entry, index) => (
                          <div key={index} className="mb-4">
                            <div className="flex items-center mb-1">
                              <span className="font-bold">{entry.speaker}</span>
                              <span className="text-gray-400 text-xs ml-2">
                                {entry.timestamp.toLocaleTimeString()}
                              </span>
                            </div>
                            <div className={`px-4 py-2 rounded-lg ${
                              entry.speaker === "You" 
                                ? "bg-primary text-primary-foreground ml-0 mr-8" 
                                : "bg-secondary text-secondary-foreground ml-8 mr-0"
                            }`}>
                              {entry.text}
                            </div>
                          </div>
                        ))}
                        <div ref={transcriptEndRef} />
                      </div>
                    )}
                  </ScrollArea>
                  
                  {summary && (
                    <div className="mt-4 p-4 border rounded-md bg-muted">
                      <h3 className="font-bold mb-2">Session Summary</h3>
                      <p>{summary}</p>
                    </div>
                  )}
                </>
              )}
            </CardContent>
            {sessionActive && (
              <CardFooter className="flex justify-between">
                <div className="flex space-x-2">
                  <Button
                    onClick={() => setIsRecording(prev => !prev)}
                    variant={isRecording ? "destructive" : "outline"}
                    className={isRecording ? "bg-red-500 hover:bg-red-600" : ""}
                  >
                    {isRecording ? (
                      <>
                        <MicOff className="mr-2 h-4 w-4" /> 
                        Pause Mic
                      </>
                    ) : (
                      <>
                        <Mic className="mr-2 h-4 w-4" /> 
                        Resume Mic
                      </>
                    )}
                  </Button>

                </div>
                
                <div className="space-x-2">
                  <Button 
                    variant="outline" 
                    onClick={exportTranscript}
                    disabled={transcript.length === 0}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export Transcript
                  </Button>
                  
                  <Button 
                    variant="default" 
                    onClick={endSession}
                  >
                    End Session & Summarize
                  </Button>
                </div>
              </CardFooter>
            )}
          </Card>
        </div>
        
        <div className="lg:col-span-1">
          <Tabs defaultValue="settings">
            <TabsList className="w-full">
              <TabsTrigger value="settings" className="flex-1">Settings</TabsTrigger>
              <TabsTrigger value="logs" className="flex-1">Debug Logs</TabsTrigger>
            </TabsList>
            
            <TabsContent value="settings">
              <Card>
                <CardHeader>
                  <CardTitle>Voice Assistant Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <h3 className="text-md font-medium mb-2">Audio Diagnostics</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Browser Support:</span>
                      <span className={audioSupported ? "text-green-500" : "text-red-500"}>
                        {audioSupported ? "✓ Supported" : "✗ Not Supported"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Microphone Permission:</span>
                      <span className={micPermissionGranted ? "text-green-500" : "text-gray-500"}>
                        {micPermissionGranted ? "✓ Granted" : "Not Requested"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Audio Context:</span>
                      <span className={audioContextState === "running" ? "text-green-500" : "text-yellow-500"}>
                        {audioContextState}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Audio Stream:</span>
                      <span className={audioStreamActive ? "text-green-500" : "text-gray-500"}>
                        {audioStreamActive ? "✓ Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={testAudioRecording}
                        disabled={sessionActive}
                      >
                        Test Recording
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={testWebSocketConnection}
                        disabled={sessionActive}
                      >
                        Test WebSocket
                      </Button>
                    </div>
                    {sessionActive && (
                      <div className="space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={generateTestAudio}
                        >
                          Generate Test Audio
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testRoundTripAudio}
                        >
                          Test Round-Trip
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testSpeechDetection}
                          className="bg-yellow-50 border-yellow-300 text-yellow-800 hover:bg-yellow-100"
                        >
                          🔍 Test Speech Detection
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testVoiceSystemComprehensive}
                          className="bg-green-50 border-green-300 text-green-800 hover:bg-green-100"
                        >
                          🚀 Comprehensive Test
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testRealTimeAudioFlow}
                          className="bg-purple-50 border-purple-300 text-purple-800 hover:bg-purple-100"
                        >
                          🔍 Real-Time Audio Debug
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={testDirectAudioSend}
                          className="bg-red-50 border-red-300 text-red-800 hover:bg-red-100"
                        >
                          🎯 Direct Audio Test
                        </Button>
                      </div>
                    )}
                  </div>

                  <Separator className="my-4" />

                  <h3 className="text-md font-medium mb-2">Microphone Settings</h3>
                  <div className="space-y-2">
                    <Label htmlFor="mic-select">Microphone Device</Label>
                    <div className="flex gap-2 items-center">
                      <Select
                        value={selectedMicDevice}
                        onValueChange={setSelectedMicDevice}
                        disabled={sessionActive}
                      >
                        <SelectTrigger id="mic-select" className="flex-1">
                          <SelectValue placeholder="Select microphone" />
                        </SelectTrigger>
                        <SelectContent>
                          {micDevices.length === 0 ? (
                            <SelectItem value="no-devices" disabled>No microphones detected</SelectItem>
                          ) : (
                            micDevices.map((device) => (
                              <SelectItem key={device.deviceId || 'unknown'} value={device.deviceId || 'default'}>
                                {device.label || `Microphone ${(device.deviceId || 'unknown').substr(0, 5)}...`}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={fetchMicrophoneDevices}
                        disabled={sessionActive}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Select your preferred microphone device for audio input.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label>Microphone Level</Label>
                      <span className={`text-xs ${audioInputLevel > 0.1 ? 'text-green-500' : 'text-gray-500'}`}>
                        {isRecording ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="h-8 border rounded-md overflow-hidden">
                      <div
                        className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-75`}
                        style={{ width: `${Math.min(100, audioInputLevel * 100)}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-500">
                      Visualizes microphone input level when recording.
                    </p>
                  </div>

                  <Separator className="my-4" />
                  
                  <h3 className="text-md font-medium mb-2">Voice Output Settings</h3>
                  <div className="space-y-2">
                    <Label htmlFor="voice-select">AI Voice</Label>
                    <Select
                      value={selectedVoice}
                      onValueChange={setSelectedVoice}
                      disabled={sessionActive || audioOutputPlaying}
                    >
                      <SelectTrigger id="voice-select">
                        <SelectValue placeholder="Select voice" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="alloy">Alloy</SelectItem>
                        <SelectItem value="echo">Echo</SelectItem>
                        <SelectItem value="fable">Fable</SelectItem>
                        <SelectItem value="onyx">Onyx</SelectItem>
                        <SelectItem value="nova">Nova</SelectItem>
                        <SelectItem value="shimmer">Shimmer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="voice-speed">
                      Voice Speed: {voiceSpeed.toFixed(2)}
                    </Label>
                    <Slider
                      id="voice-speed"
                      min={0.5}
                      max={2.0}
                      step={0.05}
                      value={[voiceSpeed]}
                      onValueChange={(values) => setVoiceSpeed(values[0])}
                      disabled={sessionActive || audioOutputPlaying}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Slower</span>
                      <span>Faster</span>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={testAudio}
                    disabled={audioOutputPlaying || (!wsRef.current && sessionActive)}
                    className="w-full flex gap-2"
                  >
                    {audioOutputPlaying ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Volume2 className="h-4 w-4" />
                    )}
                    {audioOutputPlaying ? "Playing..." : "Test Audio Output"}
                  </Button>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label htmlFor="temperature">
                      AI Creativity: {temperature.toFixed(2)}
                    </Label>
                    <Slider
                      id="temperature"
                      min={0.1}
                      max={1.0}
                      step={0.05}
                      value={[temperature]}
                      onValueChange={(values) => setTemperature(values[0])}
                      disabled={sessionActive}
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Precise</span>
                      <span>Creative</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="logs">
              <Card>
                <CardHeader>
                  <CardTitle>System Logs</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-black text-green-400 font-mono text-xs p-4 rounded-md h-80 overflow-y-auto">
                    {responseLog.length === 0 ? (
                      <p>No logs yet</p>
                    ) : (
                      responseLog.map((log, idx) => (
                        <div key={idx}>{log}</div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AdminAITest;